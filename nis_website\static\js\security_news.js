// NIS社团网站 - 安全新闻功能模块

class SecurityNewsManager {
    constructor() {
        this.currentPage = 1;
        this.currentCategory = 'all';
        this.searchQuery = '';
        this.isLoading = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadNews();
        this.loadSidebarData();
    }

    setupEventListeners() {
        // 分类过滤
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.handleCategoryFilter(e.target.closest('.filter-tab'));
            });
        });

        // 搜索功能
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.handleSearch(e.target.value);
                }, 500);
            });
        }

        // 加载更多
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreNews();
            });
        }
    }

    handleCategoryFilter(tab) {
        // 更新活跃状态
        document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');

        // 重置并加载新分类
        this.currentCategory = tab.dataset.category;
        this.currentPage = 1;
        this.loadNews(true);
    }

    handleSearch(query) {
        this.searchQuery = query.trim();
        this.currentPage = 1;
        this.loadNews(true);
    }

    async loadNews(reset = false) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        const container = document.getElementById('news-container');
        
        if (reset) {
            container.innerHTML = this.getLoadingSkeleton();
        }

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                category: this.currentCategory,
                search: this.searchQuery,
                limit: 10
            });

            const response = await fetch(`/api/security-news?${params}`);
            const data = await response.json();

            if (reset) {
                container.innerHTML = '';
            }

            if (data.success && data.data && data.data.news && data.data.news.length > 0) {
                data.data.news.forEach(newsLink => {
                    container.appendChild(this.createNewsLinkCard(newsLink));
                });

                // 更新加载更多按钮
                const loadMoreBtn = document.getElementById('load-more-btn');
                if (data.data.has_more) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            } else if (reset) {
                container.innerHTML = this.getEmptyState();
            }

        } catch (error) {
            console.error('加载新闻失败:', error);
            if (reset) {
                container.innerHTML = this.getErrorState();
            }
        } finally {
            this.isLoading = false;
        }
    }

    async loadMoreNews() {
        this.currentPage++;
        await this.loadNews(false);
    }

    createNewsLinkCard(newsLink) {
        const card = document.createElement('div');
        card.className = `news-card card category-${this.getCategoryClass(newsLink.category_id)} fade-in`;

        const createTime = new Date(newsLink.created_at).toLocaleDateString('zh-CN');

        // 获取URL预览信息
        const urlPreview = this.getUrlPreview(newsLink.url);

        card.innerHTML = `
            <div class="card-body">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-blue-600">${this.getCategoryName(newsLink.category_id)}</span>
                        ${newsLink.is_featured ? '<span class="featured-badge">推荐</span>' : ''}
                    </div>
                    <span class="news-meta">${createTime}</span>
                </div>

                <h3 class="text-xl font-bold text-gray-800 mb-3 line-clamp-2 hover:text-blue-600 cursor-pointer"
                    onclick="this.openNewsLink('${newsLink.url}', ${newsLink.id})">
                    ${newsLink.title}
                </h3>

                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-link text-gray-400 mr-2"></i>
                        <span class="text-sm text-gray-600 truncate">${urlPreview}</span>
                    </div>
                    ${newsLink.description ? `<p class="text-sm text-gray-600">${newsLink.description}</p>` : ''}
                </div>

                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-eye mr-1"></i>${newsLink.views || 0} 次浏览</span>
                        <span><i class="fas fa-clock mr-1"></i>${this.getTimeAgo(newsLink.created_at)}</span>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="window.securityNewsManager.previewLink('${newsLink.url}')"
                                class="btn btn-outline btn-sm">
                            <i class="fas fa-eye mr-1"></i>预览
                        </button>
                        <button onclick="window.securityNewsManager.openNewsLink('${newsLink.url}', ${newsLink.id})"
                                class="btn btn-primary btn-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>访问
                        </button>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    getUrlPreview(url) {
        // 简单的URL预览信息提取
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname;

            // 根据域名返回不同的预览信息
            const previewMap = {
                'freebuf.com': { icon: '🛡️', source: 'FreeBuf', color: 'blue' },
                'anquanke.com': { icon: '🔒', source: '安全客', color: 'green' },
                'seebug.org': { icon: '🐛', source: 'Seebug', color: 'red' },
                'xz.aliyun.com': { icon: '☁️', source: '先知社区', color: 'orange' },
                'nsfocus.net': { icon: '🔍', source: '绿盟科技', color: 'purple' }
            };

            for (const [key, value] of Object.entries(previewMap)) {
                if (domain.includes(key)) {
                    return value;
                }
            }

            return { icon: '🌐', source: domain, color: 'gray' };
        } catch (e) {
            return { icon: '🌐', source: '未知来源', color: 'gray' };
        }
    }

    getCategoryClass(categoryId) {
        const categoryMap = {
            1: 'security',
            2: 'vulnerability',
            3: 'analysis',
            4: 'industry'
        };
        return categoryMap[categoryId] || 'default';
    }

    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return '刚刚';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`;

        return date.toLocaleDateString('zh-CN');
    }

    async openNewsLink(url, linkId) {
        // 增加浏览次数
        try {
            await fetch(`/api/news-links/${linkId}/view`, { method: 'POST' });
        } catch (error) {
            console.error('更新浏览次数失败:', error);
        }

        // 打开链接
        window.open(url, '_blank');
    }

    previewLink(url) {
        // 创建预览模态框
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-96">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">链接预览</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">链接地址:</label>
                        <div class="flex">
                            <input type="text" value="${url}" readonly
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg bg-gray-50">
                            <button onclick="navigator.clipboard.writeText('${url}')"
                                    class="px-4 py-2 bg-blue-500 text-white rounded-r-lg hover:bg-blue-600">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <button onclick="window.open('${url}', '_blank')"
                                class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>访问链接
                        </button>
                        <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    getCategoryClass(categoryId) {
        const classes = {
            1: 'vulnerability',
            2: 'threat',
            3: 'tools',
            4: 'industry',
            5: 'research',
            6: 'policy'
        };
        return classes[categoryId] || 'industry';
    }

    getCategoryName(categoryId) {
        const names = {
            1: '漏洞预警',
            2: '威胁情报',
            3: '安全工具',
            4: '行业动态',
            5: '技术研究',
            6: '法规政策'
        };
        return names[categoryId] || '其他';
    }

    async loadSidebarData() {
        try {
            // 模拟数据，实际项目中从API获取
            const mockHotNews = [
                { title: '新型勒索软件攻击全球企业', original_url: '#', view_count: 1250 },
                { title: 'Chrome浏览器发现严重安全漏洞', original_url: '#', view_count: 980 },
                { title: '国家网络安全法最新修订', original_url: '#', view_count: 756 },
                { title: 'AI安全威胁分析报告发布', original_url: '#', view_count: 642 },
                { title: '零日漏洞利用工具包曝光', original_url: '#', view_count: 534 }
            ];

            const mockCategories = [
                { id: 1, name: '漏洞预警', count: 45 },
                { id: 2, name: '威胁情报', count: 32 },
                { id: 3, name: '安全工具', count: 28 },
                { id: 4, name: '行业动态', count: 67 },
                { id: 5, name: '技术研究', count: 23 },
                { id: 6, name: '法规政策', count: 15 }
            ];

            const mockSources = [
                { name: 'FreeBuf', logo_url: '', article_count: 89 },
                { name: '安全客', logo_url: '', article_count: 76 },
                { name: 'Seebug', logo_url: '', article_count: 54 },
                { name: '先知社区', logo_url: '', article_count: 43 },
                { name: '绿盟科技', logo_url: '', article_count: 32 }
            ];

            this.renderHotNews(mockHotNews);
            this.renderCategoryStats(mockCategories);
            this.renderNewsSources(mockSources);

        } catch (error) {
            console.error('加载侧边栏数据失败:', error);
        }
    }

    renderHotNews(articles) {
        const container = document.getElementById('hot-news');
        if (!container) return;

        container.innerHTML = articles.slice(0, 5).map((article, index) => `
            <div class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                 onclick="window.open('${article.original_url}', '_blank')">
                <span class="flex-shrink-0 w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                    ${index + 1}
                </span>
                <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-gray-800 line-clamp-2">${article.title}</h4>
                    <p class="text-xs text-gray-500 mt-1">${article.view_count || 0} 阅读</p>
                </div>
            </div>
        `).join('');
    }

    renderCategoryStats(categories) {
        const container = document.getElementById('categories-stats');
        if (!container) return;

        container.innerHTML = categories.map(cat => `
            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer"
                 onclick="document.querySelector('[data-category=\\"${cat.id}\\"]').click()">
                <div class="flex items-center">
                    <i class="${this.getCategoryIcon(cat.id)} text-${this.getCategoryColor(cat.id)}-500 mr-2"></i>
                    <span class="text-sm text-gray-700">${cat.name}</span>
                </div>
                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${cat.count}</span>
            </div>
        `).join('');
    }

    renderNewsSources(sources) {
        const container = document.getElementById('news-sources');
        if (!container) return;

        container.innerHTML = sources.slice(0, 5).map(source => `
            <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-blue-100 rounded mr-2 flex items-center justify-center">
                        <i class="fas fa-rss text-blue-500 text-xs"></i>
                    </div>
                    <span class="text-sm text-gray-700">${source.name}</span>
                </div>
                <span class="text-xs text-gray-500">${source.article_count}</span>
            </div>
        `).join('');
    }

    getCategoryIcon(categoryId) {
        const icons = {
            1: 'fas fa-exclamation-triangle',
            2: 'fas fa-shield-alt',
            3: 'fas fa-tools',
            4: 'fas fa-newspaper',
            5: 'fas fa-microscope',
            6: 'fas fa-gavel'
        };
        return icons[categoryId] || 'fas fa-newspaper';
    }

    getCategoryColor(categoryId) {
        const colors = {
            1: 'red',
            2: 'yellow',
            3: 'green',
            4: 'blue',
            5: 'purple',
            6: 'gray'
        };
        return colors[categoryId] || 'blue';
    }

    getLoadingSkeleton() {
        return Array(3).fill(0).map(() => `
            <div class="card">
                <div class="card-body">
                    <div class="loading-skeleton h-6 w-1/3 mb-3"></div>
                    <div class="loading-skeleton h-8 w-full mb-3"></div>
                    <div class="loading-skeleton h-32 w-full mb-4"></div>
                    <div class="loading-skeleton h-4 w-2/3"></div>
                </div>
            </div>
        `).join('');
    }

    getEmptyState() {
        return `
            <div class="text-center py-12">
                <i class="fas fa-newspaper text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-600 mb-2">暂无相关新闻</h3>
                <p class="text-gray-500">爬虫正在收集最新安全资讯，请稍后查看</p>
                <button onclick="location.reload()" class="btn btn-primary mt-4">刷新页面</button>
            </div>
        `;
    }

    getErrorState() {
        return `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-600 mb-2">加载失败</h3>
                <p class="text-gray-500 mb-4">请检查网络连接或稍后重试</p>
                <button onclick="location.reload()" class="btn btn-primary">重新加载</button>
            </div>
        `;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new SecurityNewsManager();
});
