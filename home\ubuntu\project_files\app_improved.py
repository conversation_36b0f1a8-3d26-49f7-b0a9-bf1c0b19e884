#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - 改进版
修复安全漏洞和代码逻辑问题
"""

import os
import logging
import json
import secrets
from datetime import datetime, timedelta
from functools import wraps
from flask import Flask, request, jsonify, send_from_directory, session
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv
import hashlib
import hmac

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 安全配置
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_hex(32))
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# 配置CORS
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:5000'], 
     supports_credentials=True)

# 配置限流器
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://"
)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'root'),
    'database': os.getenv('DB_NAME', 'nis_security_news'),
    'charset': 'utf8mb4',
    'autocommit': True,
    'use_unicode': True,
    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
}

# 管理员配置
ADMIN_CONFIG = {
    'username': os.getenv('ADMIN_USERNAME', 'admin'),
    'password_hash': hashlib.sha256(os.getenv('ADMIN_PASSWORD', 'nis2024').encode()).hexdigest()
}

# 静态数据（作为备用）
SAMPLE_ANNOUNCEMENTS = [
    {
        'id': 1,
        'title': '欢迎使用NIS社团管理系统',
        'content': '这是一个全新的社团管理系统，包含新闻链接管理、项目展示、学习资源、活动管理等功能。',
        'type': 'notice',
        'status': 'published',
        'is_pinned': True,
        'publish_time': '2024-07-16 10:00:00',
        'created_at': '2024-07-16 10:00:00'
    },
    {
        'id': 2,
        'title': '2024年春季招新开始',
        'content': '我们正在寻找对网络安全感兴趣的同学加入我们的团队！',
        'type': 'event',
        'status': 'published',
        'is_pinned': True,
        'publish_time': '2024-07-16 09:00:00',
        'created_at': '2024-07-16 09:00:00'
    }
]

SAMPLE_NEWS_LINKS = [
    {
        'id': 1,
        'title': 'FreeBuf安全资讯',
        'url': 'https://www.freebuf.com/',
        'description': '国内知名网络安全媒体平台',
        'category': '网络安全',
        'status': 'active',
        'views': 156,
        'is_featured': True,
        'created_at': '2024-07-16 08:00:00'
    },
    {
        'id': 2,
        'title': '安全客',
        'url': 'https://www.anquanke.com/',
        'description': '专业的网络安全技术媒体',
        'category': '网络安全',
        'status': 'active',
        'views': 89,
        'is_featured': True,
        'created_at': '2024-07-16 08:00:00'
    }
]

# 示例项目数据
SAMPLE_PROJECTS = [
    {
        'id': 1,
        'title': '校园网络监控平台',
        'description': '基于Python和ELK Stack的校园网络流量监控与异常检测平台，能够实时监控网络流量并识别潜在的安全威胁。',
        'github_url': 'https://github.com/nis/network-monitor',
        'demo_url': 'http://demo.nis.edu.cn/monitor',
        'tags': ['Python', 'Elasticsearch', 'Flask', 'Docker'],
        'status': '已完成',
        'award': '获2023年四川省大学生网络安全竞赛二等奖',
        'is_featured': True,
        'created_at': '2024-01-15 10:00:00'
    },
    {
        'id': 2,
        'title': 'Web安全扫描器',
        'description': '自主开发的Web应用安全漏洞扫描工具，支持SQL注入、XSS、CSRF等常见漏洞的自动化检测。',
        'github_url': 'https://github.com/nis/web-scanner',
        'demo_url': '',
        'tags': ['Python', 'Requests', 'BeautifulSoup', 'SQLite'],
        'status': '已完成',
        'award': '获校级创新创业大赛一等奖',
        'is_featured': True,
        'created_at': '2024-02-20 14:30:00'
    }
]

# 示例团队成员数据
SAMPLE_TEAM = [
    {
        'id': 1,
        'name': '张三',
        'role': '社团主席',
        'achievement': '计算机科学与技术专业，专注于Web安全和渗透测试，获得多项网络安全竞赛奖项',
        'skills': ['Python', 'Web安全', '渗透测试', 'Linux', 'Docker'],
        'github_url': 'https://github.com/zhangsan',
        'email': '<EMAIL>',
        'join_date': '2022-09-01',
        'is_core': True
    },
    {
        'id': 2,
        'name': '李四',
        'role': '技术副主席',
        'achievement': '网络工程专业，擅长网络安全和系统管理，负责社团技术培训工作',
        'skills': ['网络安全', '系统管理', '防火墙', 'IDS', 'Wireshark'],
        'github_url': 'https://github.com/lisi',
        'email': '<EMAIL>',
        'join_date': '2022-09-01',
        'is_core': True
    }
]

# 示例学习资源数据
SAMPLE_RESOURCES = [
    {
        'id': 1,
        'title': '网络安全入门指南',
        'description': '适合初学者的网络安全学习路线图，包含基础知识、工具使用、实战案例等内容',
        'url': 'https://github.com/nis/security-guide',
        'type': 'link',
        'category': '入门教程',
        'difficulty': '初级',
        'tags': ['网络安全', '入门', '学习路线'],
        'download_count': 1250,
        'is_featured': True,
        'created_at': '2024-01-10 08:00:00'
    },
    {
        'id': 2,
        'title': '渗透测试工具集',
        'description': '常用渗透测试工具的使用教程合集，包含Nmap、Metasploit、Burp Suite等工具的详细使用方法',
        'url': '/static/files/pentest-tools.pdf',
        'type': 'file',
        'category': '工具教程',
        'difficulty': '中级',
        'tags': ['渗透测试', '工具', '教程'],
        'download_count': 890,
        'is_featured': True,
        'created_at': '2024-02-15 10:30:00'
    }
]

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def execute_query(query, params=None, fetch_one=False):
    """执行数据库查询 - 改进版，增加错误处理和SQL注入防护"""
    connection = None
    cursor = None
    try:
        connection = get_db_connection()
        if not connection:
            return None
            
        cursor = connection.cursor(dictionary=True, buffered=True)
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            if fetch_one:
                result = cursor.fetchone()
            else:
                result = cursor.fetchall()
        else:
            connection.commit()
            result = cursor.rowcount
        
        return result
        
    except Error as e:
        logger.error(f"数据库操作失败: {e}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def validate_input(data, required_fields):
    """验证输入数据"""
    if not isinstance(data, dict):
        return False, "无效的数据格式"
    
    for field in required_fields:
        if field not in data or not data[field]:
            return False, f"缺少必填字段: {field}"
    
    return True, "验证通过"

def sanitize_string(text, max_length=1000):
    """清理字符串输入"""
    if not isinstance(text, str):
        return ""
    
    # 移除潜在的恶意字符
    text = text.strip()
    text = text.replace('<script>', '').replace('</script>', '')
    text = text.replace('javascript:', '')
    
    # 限制长度
    if len(text) > max_length:
        text = text[:max_length]
    
    return text

def require_admin_auth(f):
    """管理员认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session or not session['admin_logged_in']:
            return jsonify({'success': False, 'message': '需要管理员权限'}), 401
        return f(*args, **kwargs)
    return decorated_function

def generate_csrf_token():
    """生成CSRF令牌"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(32)
    return session['csrf_token']

def validate_csrf_token(token):
    """验证CSRF令牌"""
    return 'csrf_token' in session and hmac.compare_digest(session['csrf_token'], token)

# ==================== 静态文件路由 ====================

@app.route('/')
def root():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """安全的静态文件服务"""
    # 防止目录遍历攻击
    if '..' in filename or filename.startswith('/'):
        return jsonify({'error': '非法的文件路径'}), 400
    
    # 只允许特定的文件扩展名
    allowed_extensions = {'.html', '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.pdf'}
    file_ext = os.path.splitext(filename)[1].lower()
    
    if file_ext not in allowed_extensions:
        return jsonify({'error': '不支持的文件类型'}), 400
    
    try:
        return send_from_directory('.', filename)
    except FileNotFoundError:
        return jsonify({'error': '文件不存在'}), 404

# ==================== API路由 ====================

@app.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """获取CSRF令牌"""
    return jsonify({'csrf_token': generate_csrf_token()})

@app.route('/api/announcements', methods=['GET'])
@limiter.limit("20/minute")
def get_announcements():
    """获取公告列表"""
    try:
        announcements = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            WHERE status = 'published' AND publish_time <= NOW()
            ORDER BY is_pinned DESC, publish_time DESC
            LIMIT 50
        """)
        
        if not announcements:
            announcements = SAMPLE_ANNOUNCEMENTS
        
        return jsonify({'success': True, 'data': announcements})
    except Exception as e:
        logger.error(f"获取公告失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_ANNOUNCEMENTS})

@app.route('/api/announcements/<int:announcement_id>', methods=['GET'])
@limiter.limit("30/minute")
def get_announcement_detail(announcement_id):
    """获取公告详情"""
    try:
        # 验证ID范围
        if announcement_id <= 0 or announcement_id > 999999:
            return jsonify({'success': False, 'message': '无效的公告ID'}), 400
            
        announcement = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            WHERE id = %s AND status = 'published' AND publish_time <= NOW()
        """, (announcement_id,), fetch_one=True)
        
        if not announcement:
            return jsonify({'success': False, 'message': '公告不存在'}), 404
        
        return jsonify({'success': True, 'data': announcement})
    except Exception as e:
        logger.error(f"获取公告详情失败: {e}")
        return jsonify({'success': False, 'message': '获取公告详情失败'}), 500

@app.route('/api/news-links', methods=['GET'])
@limiter.limit("20/minute")
def get_news_links():
    """获取新闻链接列表"""
    try:
        page = max(1, int(request.args.get('page', 1)))
        limit = min(50, max(1, int(request.args.get('limit', 10))))
        offset = (page - 1) * limit
        
        links = execute_query("""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE nl.status = 'active'
            ORDER BY nl.is_featured DESC, nl.created_at DESC
            LIMIT %s OFFSET %s
        """, (limit, offset))
        
        if not links:
            links = SAMPLE_NEWS_LINKS
        
        return jsonify({'success': True, 'data': links})
    except ValueError:
        return jsonify({'success': False, 'message': '无效的分页参数'}), 400
    except Exception as e:
        logger.error(f"获取新闻链接失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_NEWS_LINKS})

@app.route('/api/categories', methods=['GET'])
@limiter.limit("20/minute")
def get_categories():
    """获取新闻分类"""
    try:
        categories = execute_query("""
            SELECT id, name, description, sort_order
            FROM news_categories
            WHERE is_active = 1
            ORDER BY sort_order
            LIMIT 20
        """)

        if not categories:
            categories = [
                {'id': 1, 'name': '网络安全', 'description': '网络安全相关新闻'},
                {'id': 2, 'name': '漏洞情报', 'description': '安全漏洞和威胁情报'},
                {'id': 3, 'name': '技术分析', 'description': '安全技术分析文章'},
                {'id': 4, 'name': '行业动态', 'description': '安全行业动态新闻'}
            ]

        return jsonify({'success': True, 'data': categories})
    except Exception as e:
        logger.error(f"获取分类失败: {e}")
        return jsonify({'success': False, 'data': []})

@app.route('/api/security-news', methods=['GET'])
@limiter.limit("20/minute")
def get_security_news():
    """获取安全新闻列表（兼容前端调用）"""
    try:
        page = max(1, int(request.args.get('page', 1)))
        limit = min(50, max(1, int(request.args.get('limit', 10))))
        category = sanitize_string(request.args.get('category', 'all'), 50)
        search = sanitize_string(request.args.get('search', ''), 100)

        # 构建查询条件
        where_conditions = ["nl.status = 'active'"]
        params = []

        if category != 'all' and category:
            where_conditions.append("nc.name = %s")
            params.append(category)

        if search:
            where_conditions.append("(nl.title LIKE %s OR nl.description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)
        offset = (page - 1) * limit

        # 查询新闻链接
        query = f"""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE {where_clause}
            ORDER BY nl.is_featured DESC, nl.created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([limit, offset])

        links = execute_query(query, params)

        if not links:
            links = SAMPLE_NEWS_LINKS

        # 检查是否还有更多数据
        count_query = f"""
            SELECT COUNT(*) as total
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            WHERE {where_clause}
        """
        count_result = execute_query(count_query, params[:-2], fetch_one=True)
        total = count_result['total'] if count_result else len(links)
        has_more = (page * limit) < total

        return jsonify({
            'success': True,
            'data': {
                'news': links,
                'has_more': has_more,
                'total': total,
                'page': page
            }
        })
    except ValueError:
        return jsonify({'success': False, 'message': '无效的分页参数'}), 400
    except Exception as e:
        logger.error(f"获取安全新闻失败: {e}")
        return jsonify({
            'success': False,
            'data': {
                'news': SAMPLE_NEWS_LINKS,
                'has_more': False,
                'total': len(SAMPLE_NEWS_LINKS),
                'page': 1
            }
        })

@app.route('/api/news-links/<int:link_id>/view', methods=['POST'])
@limiter.limit("10/minute")
def increment_view_count(link_id):
    """增加链接浏览次数"""
    try:
        # 验证ID范围
        if link_id <= 0 or link_id > 999999:
            return jsonify({'success': False, 'message': '无效的链接ID'}), 400
            
        result = execute_query("""
            UPDATE news_links
            SET views = views + 1
            WHERE id = %s AND status = 'active'
        """, (link_id,))

        if result and result > 0:
            return jsonify({'success': True, 'message': '浏览次数已更新'})
        else:
            return jsonify({'success': False, 'message': '链接不存在'}), 404
    except Exception as e:
        logger.error(f"更新浏览次数失败: {e}")
        return jsonify({'success': False, 'message': '更新失败'}), 500

@app.route('/api/projects', methods=['GET'])
@limiter.limit("20/minute")
def get_projects():
    """获取项目列表"""
    try:
        projects = execute_query("""
            SELECT id, title, description, github_url, demo_url, technologies as tags,
                   status, award, is_featured, created_at
            FROM projects
            WHERE status != 'archived'
            ORDER BY is_featured DESC, created_at DESC
            LIMIT 50
        """)

        if not projects:
            projects = SAMPLE_PROJECTS
        else:
            # 处理JSON字段
            for project in projects:
                if project.get('tags') and isinstance(project['tags'], str):
                    try:
                        project['tags'] = json.loads(project['tags'])
                    except json.JSONDecodeError:
                        project['tags'] = []

        return jsonify({'success': True, 'data': projects})
    except Exception as e:
        logger.error(f"获取项目失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_PROJECTS})

@app.route('/api/team', methods=['GET'])
@limiter.limit("20/minute")
def get_team():
    """获取团队成员"""
    try:
        team = execute_query("""
            SELECT id, name, position as role, bio as achievement, skills,
                   github_url, email, join_date, is_core
            FROM team_members
            WHERE is_active = 1
            ORDER BY is_core DESC, sort_order ASC
            LIMIT 50
        """)

        if not team:
            team = SAMPLE_TEAM
        else:
            # 处理JSON字段
            for member in team:
                if member.get('skills') and isinstance(member['skills'], str):
                    try:
                        member['skills'] = json.loads(member['skills'])
                    except json.JSONDecodeError:
                        member['skills'] = []

        return jsonify({'success': True, 'data': team})
    except Exception as e:
        logger.error(f"获取团队信息失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_TEAM})

@app.route('/api/resources', methods=['GET'])
@limiter.limit("20/minute")
def get_resources():
    """获取学习资源"""
    try:
        resources = execute_query("""
            SELECT id, title, description, url, resource_type as type,
                   category, difficulty, tags, download_count, is_featured, created_at
            FROM learning_resources
            WHERE status = 'active'
            ORDER BY is_featured DESC, created_at DESC
            LIMIT 50
        """)

        if not resources:
            resources = SAMPLE_RESOURCES
        else:
            # 处理JSON字段
            for resource in resources:
                if resource.get('tags') and isinstance(resource['tags'], str):
                    try:
                        resource['tags'] = json.loads(resource['tags'])
                    except json.JSONDecodeError:
                        resource['tags'] = []

        return jsonify({'success': True, 'data': resources})
    except Exception as e:
        logger.error(f"获取学习资源失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_RESOURCES})

@app.route('/api/events', methods=['GET'])
@limiter.limit("20/minute")
def get_events():
    """获取活动列表"""
    try:
        events = execute_query("""
            SELECT id, title, description, content, event_date, location,
                   image_url, participants_count, status, is_featured, created_at
            FROM events
            ORDER BY event_date DESC
            LIMIT 50
        """)

        if not events:
            events = [
                {
                    'id': 1,
                    'title': '2024年网络安全技能大赛',
                    'description': '社团年度网络安全技能竞赛活动',
                    'content': '本次大赛包含Web安全、逆向工程、密码学等多个方向的挑战题目，旨在提升成员的实战技能。',
                    'event_date': '2024-03-15',
                    'location': '学校机房A301',
                    'image_url': '/static/images/event1.jpg',
                    'participants_count': 45,
                    'status': 'completed',
                    'is_featured': True,
                    'created_at': '2024-03-01 10:00:00'
                }
            ]

        return jsonify({'success': True, 'data': events})
    except Exception as e:
        logger.error(f"获取活动失败: {e}")
        return jsonify({'success': False, 'data': []})

# ==================== 管理员API ====================

@app.route('/api/admin/login', methods=['POST'])
@limiter.limit("5/minute")
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400
            
        username = sanitize_string(data.get('username', ''), 50)
        password = data.get('password', '')
        csrf_token = data.get('csrf_token', '')

        # 验证CSRF令牌
        if not validate_csrf_token(csrf_token):
            return jsonify({'success': False, 'message': 'CSRF令牌无效'}), 403

        # 验证用户名和密码
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        if (username == ADMIN_CONFIG['username'] and 
            hmac.compare_digest(password_hash, ADMIN_CONFIG['password_hash'])):
            
            session['admin_logged_in'] = True
            session['admin_username'] = username
            session['login_time'] = datetime.now().isoformat()
            session.permanent = True
            
            logger.info(f"管理员 {username} 登录成功，IP: {get_remote_address()}")
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'csrf_token': generate_csrf_token()
            })
        else:
            logger.warning(f"管理员登录失败，用户名: {username}，IP: {get_remote_address()}")
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'message': '登录失败'}), 500

@app.route('/api/admin/logout', methods=['POST'])
@require_admin_auth
def admin_logout():
    """管理员退出登录"""
    try:
        username = session.get('admin_username', 'unknown')
        session.clear()
        logger.info(f"管理员 {username} 退出登录")
        return jsonify({'success': True, 'message': '退出成功'})
    except Exception as e:
        logger.error(f"退出登录失败: {e}")
        return jsonify({'success': False, 'message': '退出失败'}), 500

@app.route('/api/admin/announcements', methods=['GET'])
@limiter.limit("20/minute")
@require_admin_auth
def admin_get_announcements():
    """管理员获取所有公告"""
    try:
        announcements = execute_query("""
            SELECT id, title, content, type, status, is_pinned, publish_time, created_at, updated_at
            FROM announcements
            ORDER BY is_pinned DESC, created_at DESC
            LIMIT 100
        """)

        if not announcements:
            announcements = SAMPLE_ANNOUNCEMENTS

        return jsonify({'success': True, 'data': announcements})
    except Exception as e:
        logger.error(f"获取公告失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_ANNOUNCEMENTS})

@app.route('/api/admin/announcements', methods=['POST'])
@limiter.limit("10/minute")
@require_admin_auth
def admin_create_announcement():
    """创建公告"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400

        # 验证CSRF令牌
        csrf_token = data.get('csrf_token', '')
        if not validate_csrf_token(csrf_token):
            return jsonify({'success': False, 'message': 'CSRF令牌无效'}), 403

        # 验证必填字段
        is_valid, message = validate_input(data, ['title', 'content', 'type'])
        if not is_valid:
            return jsonify({'success': False, 'message': message}), 400

        # 清理输入数据
        title = sanitize_string(data['title'], 200)
        content = sanitize_string(data['content'], 5000)
        announcement_type = sanitize_string(data['type'], 20)
        
        # 验证类型
        if announcement_type not in ['notice', 'event', 'urgent']:
            return jsonify({'success': False, 'message': '无效的公告类型'}), 400

        # 插入数据库
        result = execute_query("""
            INSERT INTO announcements (title, content, type, status, is_pinned, publish_time, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            title,
            content,
            announcement_type,
            data.get('status', 'published'),
            bool(data.get('is_pinned', False)),
            data.get('publish_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))

        if result and result > 0:
            logger.info(f"管理员 {session.get('admin_username')} 创建公告: {title}")
            return jsonify({'success': True, 'message': '公告创建成功'})
        else:
            return jsonify({'success': False, 'message': '创建失败'}), 500

    except Exception as e:
        logger.error(f"创建公告失败: {e}")
        return jsonify({'success': False, 'message': '创建失败'}), 500

@app.route('/api/admin/announcements/<int:announcement_id>', methods=['PUT'])
@limiter.limit("10/minute")
@require_admin_auth
def admin_update_announcement(announcement_id):
    """更新公告"""
    try:
        # 验证ID范围
        if announcement_id <= 0 or announcement_id > 999999:
            return jsonify({'success': False, 'message': '无效的公告ID'}), 400
            
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400

        # 验证CSRF令牌
        csrf_token = data.get('csrf_token', '')
        if not validate_csrf_token(csrf_token):
            return jsonify({'success': False, 'message': 'CSRF令牌无效'}), 403

        # 构建更新字段
        update_fields = []
        params = []

        allowed_fields = ['title', 'content', 'type', 'status', 'is_pinned', 'publish_time']
        for field in allowed_fields:
            if field in data:
                if field in ['title', 'content', 'type', 'status']:
                    value = sanitize_string(data[field], 5000 if field == 'content' else 200)
                else:
                    value = data[field]
                update_fields.append(f"{field} = %s")
                params.append(value)

        if not update_fields:
            return jsonify({'success': False, 'message': '没有要更新的字段'}), 400

        update_fields.append("updated_at = %s")
        params.append(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        params.append(announcement_id)

        query = f"UPDATE announcements SET {', '.join(update_fields)} WHERE id = %s"
        result = execute_query(query, params)

        if result and result > 0:
            logger.info(f"管理员 {session.get('admin_username')} 更新公告 ID: {announcement_id}")
            return jsonify({'success': True, 'message': '公告更新成功'})
        else:
            return jsonify({'success': False, 'message': '公告不存在'}), 404

    except Exception as e:
        logger.error(f"更新公告失败: {e}")
        return jsonify({'success': False, 'message': '更新失败'}), 500

@app.route('/api/admin/announcements/<int:announcement_id>', methods=['DELETE'])
@limiter.limit("10/minute")
@require_admin_auth
def admin_delete_announcement(announcement_id):
    """删除公告"""
    try:
        # 验证ID范围
        if announcement_id <= 0 or announcement_id > 999999:
            return jsonify({'success': False, 'message': '无效的公告ID'}), 400
            
        result = execute_query("DELETE FROM announcements WHERE id = %s", (announcement_id,))

        if result and result > 0:
            logger.info(f"管理员 {session.get('admin_username')} 删除公告 ID: {announcement_id}")
            return jsonify({'success': True, 'message': '公告删除成功'})
        else:
            return jsonify({'success': False, 'message': '公告不存在'}), 404

    except Exception as e:
        logger.error(f"删除公告失败: {e}")
        return jsonify({'success': False, 'message': '删除失败'}), 500

@app.route('/api/admin/news-links', methods=['GET'])
@limiter.limit("20/minute")
@require_admin_auth
def admin_get_news_links():
    """管理员获取所有新闻链接"""
    try:
        links = execute_query("""
            SELECT nl.id, nl.title, nl.url, nl.description, nl.status, nl.views, nl.is_featured,
                   nc.name as category, nl.created_at, nl.updated_at
            FROM news_links nl
            LEFT JOIN news_categories nc ON nl.category_id = nc.id
            ORDER BY nl.created_at DESC
            LIMIT 100
        """)

        if not links:
            links = SAMPLE_NEWS_LINKS

        return jsonify({'success': True, 'data': links})
    except Exception as e:
        logger.error(f"获取新闻链接失败: {e}")
        return jsonify({'success': False, 'data': SAMPLE_NEWS_LINKS})

@app.route('/api/admin/news-links', methods=['POST'])
@limiter.limit("10/minute")
@require_admin_auth
def admin_create_news_link():
    """创建新闻链接"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400

        # 验证CSRF令牌
        csrf_token = data.get('csrf_token', '')
        if not validate_csrf_token(csrf_token):
            return jsonify({'success': False, 'message': 'CSRF令牌无效'}), 403

        # 验证必填字段
        is_valid, message = validate_input(data, ['title', 'url', 'description'])
        if not is_valid:
            return jsonify({'success': False, 'message': message}), 400

        # 清理输入数据
        title = sanitize_string(data['title'], 500)
        url = sanitize_string(data['url'], 1000)
        description = sanitize_string(data['description'], 2000)

        # 验证URL格式
        if not url.startswith(('http://', 'https://')):
            return jsonify({'success': False, 'message': '无效的URL格式'}), 400

        # 获取分类ID
        category_id = 1  # 默认分类
        if data.get('category'):
            category_name = sanitize_string(data['category'], 100)
            category_result = execute_query(
                "SELECT id FROM news_categories WHERE name = %s",
                (category_name,),
                fetch_one=True
            )
            if category_result:
                category_id = category_result['id']

        # 插入数据库
        result = execute_query("""
            INSERT INTO news_links (title, url, description, category_id, status, is_featured, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            title,
            url,
            description,
            category_id,
            data.get('status', 'active'),
            bool(data.get('is_featured', False)),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))

        if result and result > 0:
            logger.info(f"管理员 {session.get('admin_username')} 创建链接: {title}")
            return jsonify({'success': True, 'message': '链接创建成功'})
        else:
            return jsonify({'success': False, 'message': '创建失败'}), 500

    except Exception as e:
        logger.error(f"创建链接失败: {e}")
        return jsonify({'success': False, 'message': '创建失败'}), 500

@app.route('/api/admin/news-links/<int:link_id>', methods=['DELETE'])
@limiter.limit("10/minute")
@require_admin_auth
def admin_delete_news_link(link_id):
    """删除新闻链接"""
    try:
        # 验证ID范围
        if link_id <= 0 or link_id > 999999:
            return jsonify({'success': False, 'message': '无效的链接ID'}), 400
            
        result = execute_query("DELETE FROM news_links WHERE id = %s", (link_id,))

        if result and result > 0:
            logger.info(f"管理员 {session.get('admin_username')} 删除链接 ID: {link_id}")
            return jsonify({'success': True, 'message': '链接删除成功'})
        else:
            return jsonify({'success': False, 'message': '链接不存在'}), 404

    except Exception as e:
        logger.error(f"删除链接失败: {e}")
        return jsonify({'success': False, 'message': '删除失败'}), 500

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'message': '页面不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"服务器内部错误: {error}")
    return jsonify({'success': False, 'message': '服务器内部错误'}), 500

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({'success': False, 'message': '请求过于频繁，请稍后再试'}), 429

@app.errorhandler(403)
def forbidden(error):
    return jsonify({'success': False, 'message': '访问被禁止'}), 403

@app.errorhandler(400)
def bad_request(error):
    return jsonify({'success': False, 'message': '请求格式错误'}), 400

# ==================== 启动应用 ====================

if __name__ == '__main__':
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    logger.info("🚀 NIS社团网站启动中...")
    logger.info("📊 改进版管理系统")
    logger.info("🔒 已启用安全增强功能")
    logger.info("🌐 访问地址: http://127.0.0.1:5000")
    logger.info("🔧 管理后台: http://127.0.0.1:5000/admin_simple.html")
    logger.info("🔐 管理员账号: admin / nis2024")
    
    app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)

