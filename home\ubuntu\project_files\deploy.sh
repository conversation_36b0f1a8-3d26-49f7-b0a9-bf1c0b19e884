#!/bin/bash
# NIS网络信息安全社团网站 - 部署脚本

set -e  # 遇到错误立即退出

echo "🚀 NIS网络信息安全社团网站 - 自动部署脚本"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    log_success "Python3 已安装: $(python3 --version)"
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装"
        exit 1
    fi
    log_success "pip3 已安装: $(pip3 --version)"
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        log_warning "MySQL 未安装，正在安装..."
        sudo apt update
        sudo apt install -y mysql-server
    fi
    log_success "MySQL 已安装"
}

# 安装Python依赖
install_dependencies() {
    log_info "安装Python依赖包..."
    
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        log_success "依赖包安装完成"
    else
        log_warning "requirements.txt 不存在，手动安装依赖..."
        pip3 install flask flask-limiter mysql-connector-python flask-cors python-dotenv
        log_success "手动安装依赖完成"
    fi
}

# 配置MySQL
setup_mysql() {
    log_info "配置MySQL数据库..."
    
    # 启动MySQL服务
    sudo systemctl start mysql
    sudo systemctl enable mysql
    
    # 设置root密码（如果需要）
    if ! mysql -u root -proot -e "SELECT 1;" &> /dev/null; then
        log_info "设置MySQL root密码..."
        sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'root';"
    fi
    
    log_success "MySQL配置完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    if [ -f "database_setup_fixed.py" ]; then
        python3 database_setup_fixed.py
        log_success "数据库初始化完成"
    else
        log_error "数据库初始化脚本不存在"
        exit 1
    fi
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_info "已从 .env.example 复制配置文件"
        else
            # 创建默认配置
            cat > .env << EOF
# NIS社团网站环境变量配置
FLASK_ENV=production
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=root
DB_NAME=nis_security_news
ADMIN_USERNAME=admin
ADMIN_PASSWORD=nis2024
LOG_LEVEL=INFO
RATE_LIMIT_DEFAULT=200 per day
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:5000
CACHE_TIMEOUT=300
PORT=5000
EOF
            log_info "已创建默认配置文件"
        fi
    fi
    
    log_success "环境变量配置完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p static/images
    mkdir -p static/css
    mkdir -p static/js
    mkdir -p static/videos
    
    log_success "目录创建完成"
}

# 测试应用
test_application() {
    log_info "测试应用..."
    
    # 启动应用（后台运行）
    python3 app_production.py &
    APP_PID=$!
    
    # 等待应用启动
    sleep 5
    
    # 测试健康检查
    if curl -f http://127.0.0.1:5000/health &> /dev/null; then
        log_success "应用测试通过"
        kill $APP_PID
    else
        log_error "应用测试失败"
        kill $APP_PID
        exit 1
    fi
}

# 生成启动脚本
generate_startup_script() {
    log_info "生成启动脚本..."
    
    cat > start.sh << 'EOF'
#!/bin/bash
# NIS网络信息安全社团网站 - 启动脚本

echo "🚀 启动NIS网络信息安全社团网站..."

# 检查MySQL服务
if ! systemctl is-active --quiet mysql; then
    echo "启动MySQL服务..."
    sudo systemctl start mysql
fi

# 检查端口占用
if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口5000已被占用，正在停止现有进程..."
    pkill -f "python.*app_production.py"
    sleep 2
fi

# 启动应用
echo "启动Flask应用..."
python3 app_production.py

EOF
    
    chmod +x start.sh
    
    cat > stop.sh << 'EOF'
#!/bin/bash
# NIS网络信息安全社团网站 - 停止脚本

echo "🛑 停止NIS网络信息安全社团网站..."

# 停止Flask应用
pkill -f "python.*app_production.py"

echo "应用已停止"
EOF
    
    chmod +x stop.sh
    
    log_success "启动脚本生成完成"
}

# 生成systemd服务文件
generate_systemd_service() {
    log_info "生成systemd服务文件..."
    
    CURRENT_DIR=$(pwd)
    USER=$(whoami)
    
    cat > nis-website.service << EOF
[Unit]
Description=NIS Network Security Club Website
After=network.target mysql.service
Requires=mysql.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin
ExecStart=/usr/bin/python3 $CURRENT_DIR/app_production.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    log_info "systemd服务文件已生成: nis-website.service"
    log_info "要安装服务，请运行:"
    log_info "  sudo cp nis-website.service /etc/systemd/system/"
    log_info "  sudo systemctl daemon-reload"
    log_info "  sudo systemctl enable nis-website"
    log_info "  sudo systemctl start nis-website"
}

# 生成部署文档
generate_deployment_docs() {
    log_info "生成部署文档..."
    
    cat > DEPLOYMENT.md << 'EOF'
# NIS网络信息安全社团网站 - 部署指南

## 快速部署

### 1. 自动部署（推荐）
```bash
chmod +x deploy.sh
./deploy.sh
```

### 2. 手动部署

#### 安装依赖
```bash
# 安装Python依赖
pip3 install -r requirements.txt

# 安装MySQL（如果未安装）
sudo apt update
sudo apt install -y mysql-server
```

#### 配置数据库
```bash
# 启动MySQL
sudo systemctl start mysql
sudo systemctl enable mysql

# 设置root密码
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'root';"

# 初始化数据库
python3 database_setup_fixed.py
```

#### 配置环境
```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 启动应用
```bash
# 开发环境
python3 app_production.py

# 生产环境（使用systemd）
sudo cp nis-website.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable nis-website
sudo systemctl start nis-website
```

## 访问地址

- 主页: http://localhost:5000
- 管理后台: http://localhost:5000/admin_simple.html
- API文档: http://localhost:5000/health

## 默认账号

- 管理员用户名: admin
- 管理员密码: nis2024

## 故障排除

### 1. 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 重启MySQL
sudo systemctl restart mysql
```

### 2. 端口占用
```bash
# 查看端口占用
lsof -i :5000

# 停止占用进程
pkill -f "python.*app_production.py"
```

### 3. 权限问题
```bash
# 确保文件权限正确
chmod +x *.sh
chmod 644 *.py *.html *.css *.js
```

## 监控和维护

### 查看日志
```bash
# 应用日志
tail -f logs/app.log

# 系统服务日志
sudo journalctl -u nis-website -f
```

### 备份数据库
```bash
mysqldump -u root -proot nis_security_news > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 更新应用
```bash
# 停止服务
sudo systemctl stop nis-website

# 更新代码
git pull  # 如果使用git

# 重启服务
sudo systemctl start nis-website
```
EOF
    
    log_success "部署文档生成完成"
}

# 主函数
main() {
    log_info "开始部署NIS网络信息安全社团网站..."
    
    # 检查是否在正确的目录
    if [ ! -f "app_production.py" ]; then
        log_error "未找到 app_production.py，请确保在正确的项目目录中运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_requirements
    install_dependencies
    setup_mysql
    setup_environment
    create_directories
    init_database
    generate_startup_script
    generate_systemd_service
    generate_deployment_docs
    test_application
    
    echo ""
    log_success "🎉 部署完成！"
    echo "=================================================="
    log_info "访问地址:"
    log_info "  主页: http://localhost:5000"
    log_info "  管理后台: http://localhost:5000/admin_simple.html"
    echo ""
    log_info "启动应用:"
    log_info "  ./start.sh"
    echo ""
    log_info "停止应用:"
    log_info "  ./stop.sh"
    echo ""
    log_info "管理员账号:"
    log_info "  用户名: admin"
    log_info "  密码: nis2024"
    echo ""
    log_info "详细部署文档请查看: DEPLOYMENT.md"
}

# 运行主函数
main "$@"

