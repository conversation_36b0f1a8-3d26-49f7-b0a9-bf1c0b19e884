<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目展示 - NIS网络信息安全社团</title>
    <meta name="description" content="NIS社团项目展示，包括网络安全工具、研究项目和获奖作品">
    <meta name="keywords" content="网络安全项目,安全工具,研究项目,获奖作品,开源项目">
    
    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style_improved.css">
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white/90 backdrop-blur-sm shadow-sm sticky top-0 z-50 transition-all duration-300">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shield-alt text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-800">NIS安全社团</h1>
                    <p class="text-xs text-gray-600">网络信息安全社团</p>
                </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-600 hover:text-blue-600 transition-colors">首页</a>
                <a href="security_news.html" class="text-gray-600 hover:text-blue-600 transition-colors">安全资讯</a>
                <a href="projects.html" class="text-blue-600 font-semibold">项目展示</a>
                <a href="resources.html" class="text-gray-600 hover:text-blue-600 transition-colors">学习资源</a>
                <a href="events.html" class="text-gray-600 hover:text-blue-600 transition-colors">活动回顾</a>
                <a href="about.html" class="text-gray-600 hover:text-blue-600 transition-colors">关于我们</a>
            </div>
            
            <div class="md:hidden">
                <button class="text-gray-600 hover:text-blue-600">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-20">
        <div class="container mx-auto px-6 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                项目展示
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90">
                展示我们的创新项目和技术成果
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-code mr-2"></i>
                    开源项目
                </div>
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-trophy mr-2"></i>
                    获奖作品
                </div>
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-rocket mr-2"></i>
                    创新研究
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div class="flex flex-wrap gap-2">
                    <button class="filter-btn active" data-status="all">全部项目</button>
                    <button class="filter-btn" data-status="completed">已完成</button>
                    <button class="filter-btn" data-status="developing">开发中</button>
                    <button class="filter-btn" data-status="planning">规划中</button>
                </div>
                <div class="flex gap-2">
                    <button class="sort-btn" data-sort="featured">推荐项目</button>
                    <button class="sort-btn" data-sort="latest">最新项目</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div id="projects-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 项目卡片将通过JavaScript动态加载 -->
            </div>
            
            <!-- Loading -->
            <div id="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-2 text-gray-600">加载中...</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">NIS安全社团</h3>
                            <p class="text-gray-400">网络信息安全社团</p>
                        </div>
                    </div>
                    <p class="text-gray-400 mb-6">
                        致力于网络安全技术研究与实践，培养优秀的网络安全人才，为网络空间安全贡献力量。
                    </p>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">快速链接</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">首页</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">关于我们</a></li>
                        <li><a href="security_news.html" class="text-gray-400 hover:text-white transition-colors">安全资讯</a></li>
                        <li><a href="resources.html" class="text-gray-400 hover:text-white transition-colors">学习资源</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li class="text-gray-400">
                            <i class="fas fa-envelope mr-2"></i>
                            <EMAIL>
                        </li>
                        <li class="text-gray-400">
                            <i class="fab fa-qq mr-2"></i>
                            QQ群: 123456789
                        </li>
                        <li class="text-gray-400">
                            <i class="fab fa-github mr-2"></i>
                            GitHub: NIS-Security
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 NIS网络信息安全社团. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/common_improved.js"></script>
    <script src="/static/js/api.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProjects();
            initFilters();
        });

        // 加载项目
        function loadProjects() {
            const container = document.getElementById('projects-container');
            const loading = document.getElementById('loading');
            
            fetch('/api/projects')
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    if (data.success && data.data) {
                        renderProjectCards(data.data);
                    } else {
                        container.innerHTML = '<div class="col-span-full text-center text-gray-500">暂无项目展示</div>';
                    }
                })
                .catch(error => {
                    console.error('加载项目失败:', error);
                    loading.style.display = 'none';
                    container.innerHTML = '<div class="col-span-full text-center text-red-500">加载失败，请稍后重试</div>';
                });
        }

        // 渲染项目卡片
        function renderProjectCards(projects) {
            const container = document.getElementById('projects-container');
            container.innerHTML = projects.map(project => `
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                ${getStatusText(project.status)}
                            </span>
                            ${project.is_featured ? '<i class="fas fa-star text-yellow-500"></i>' : ''}
                        </div>
                        <h3 class="text-lg font-semibold mb-3">${project.title}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">${project.description || '暂无描述'}</p>
                        
                        ${project.tags ? `
                        <div class="flex flex-wrap gap-1 mb-4">
                            ${(Array.isArray(project.tags) ? project.tags : JSON.parse(project.tags || '[]')).map(tag => 
                                `<span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">${tag}</span>`
                            ).join('')}
                        </div>
                        ` : ''}
                        
                        ${project.award ? `
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-trophy text-yellow-600 mr-2"></i>
                                <span class="text-yellow-800 text-sm font-medium">${project.award}</span>
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="flex items-center justify-between">
                            <div class="flex gap-2">
                                ${project.github_url ? `
                                <a href="${project.github_url}" target="_blank" 
                                   class="text-gray-600 hover:text-blue-600 transition-colors">
                                    <i class="fab fa-github text-lg"></i>
                                </a>
                                ` : ''}
                                ${project.demo_url ? `
                                <a href="${project.demo_url}" target="_blank" 
                                   class="text-gray-600 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-external-link-alt text-lg"></i>
                                </a>
                                ` : ''}
                            </div>
                            <span class="text-gray-400 text-sm">${formatDate(project.created_at)}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statuses = {
                'planning': '规划中',
                'developing': '开发中',
                'completed': '已完成',
                'archived': '已归档'
            };
            return statuses[status] || '未知';
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        // 初始化过滤器
        function initFilters() {
            // 状态过滤器
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    // 这里可以添加过滤逻辑
                });
            });

            // 排序按钮
            document.querySelectorAll('.sort-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    // 这里可以添加排序逻辑
                });
            });
        }
    </script>

    <style>
        .filter-btn, .sort-btn {
            @apply px-4 py-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-100 transition-colors;
        }
        .filter-btn.active, .sort-btn.active {
            @apply bg-blue-600 text-white border-blue-600;
        }
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</body>
</html>
