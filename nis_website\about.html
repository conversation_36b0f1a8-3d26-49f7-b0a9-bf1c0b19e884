<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="csrf-token-placeholder">
    <meta name="description" content="了解NIS网络信息安全社团的历史、使命、团队成员和发展历程">
    <meta name="keywords" content="NIS社团,关于我们,团队介绍,网络安全,成都工业职业技术学院">
    <title>关于我们 - NIS网络信息安全社团</title>
    
    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style_improved.css">
    
    <style>
        .timeline {
            position: relative;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3B82F6, #8B5CF6);
            transform: translateX(-50%);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 3rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 1.5rem;
            width: 1rem;
            height: 1rem;
            background: #3B82F6;
            border: 3px solid white;
            border-radius: 50%;
            transform: translateX(-50%);
            z-index: 10;
            box-shadow: 0 0 0 3px #3B82F6;
        }
        
        .timeline-content {
            width: 45%;
            padding: 1.5rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: auto;
        }
        
        .timeline-item:nth-child(even) .timeline-content {
            margin-right: auto;
        }
        
        .timeline-content::before {
            content: '';
            position: absolute;
            top: 1.5rem;
            width: 0;
            height: 0;
            border: 10px solid transparent;
        }
        
        .timeline-item:nth-child(odd) .timeline-content::before {
            left: -20px;
            border-right-color: white;
        }
        
        .timeline-item:nth-child(even) .timeline-content::before {
            right: -20px;
            border-left-color: white;
        }
        
        @media (max-width: 768px) {
            .timeline::before {
                left: 1rem;
            }
            
            .timeline-item::before {
                left: 1rem;
            }
            
            .timeline-content {
                width: calc(100% - 3rem);
                margin-left: 3rem !important;
                margin-right: 0 !important;
            }
            
            .timeline-content::before {
                left: -20px !important;
                right: auto !important;
                border-right-color: white !important;
                border-left-color: transparent !important;
            }
        }
        
        .achievement-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B4513;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 600;
            font-size: 0.875rem;
            display: inline-block;
            margin: 0.25rem;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white/90 backdrop-blur-sm shadow-sm sticky top-0 z-50 transition-all duration-300">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <div class="h-10 w-10 mr-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <i class="fas fa-shield-alt text-white"></i>
                </div>
                <span class="font-bold text-xl text-slate-800">NIS社团</span>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-6 text-slate-600">
                <a href="index.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-home mr-1"></i>首页
                </a>
                <a href="about.html" class="nav-link text-blue-600 font-semibold">
                    <i class="fas fa-users mr-1"></i>关于我们
                </a>
                <a href="projects.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-code mr-1"></i>项目展示
                </a>
                <a href="resources.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-book mr-1"></i>学习资源
                </a>
                <a href="events.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-calendar mr-1"></i>活动回顾
                </a>
                <a href="security_news.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-newspaper mr-1"></i>安全资讯
                </a>
                <a href="announcements.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-bullhorn mr-1"></i>公告中心
                </a>
                <a href="join.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-user-plus mr-1"></i>加入我们
                </a>
            </div>
            
            <div class="md:hidden">
                <button id="menu-btn" class="text-slate-600 focus:outline-none">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </div>
        </nav>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden px-6 pb-4 bg-white border-t border-gray-200">
            <div class="space-y-2 py-4">
                <a href="index.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="about.html" class="block py-2 text-blue-600 font-semibold">
                    <i class="fas fa-users mr-2"></i>关于我们
                </a>
                <a href="projects.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-code mr-2"></i>项目展示
                </a>
                <a href="resources.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-book mr-2"></i>学习资源
                </a>
                <a href="events.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>活动回顾
                </a>
                <a href="security_news.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-newspaper mr-2"></i>安全资讯
                </a>
                <a href="announcements.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-bullhorn mr-2"></i>公告中心
                </a>
                <a href="join.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>加入我们
                </a>
            </div>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="gradient-bg text-white section-padding">
            <div class="container mx-auto px-6 text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6 fade-in">关于我们</h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto fade-in">
                    NIS（Network Information Security）网络信息安全社团，是成都工业职业技术学院技术爱好者的聚集地
                </p>
            </div>
        </section>

        <!-- Mission & Vision Section -->
        <section class="section-padding bg-white">
            <div class="container mx-auto px-6">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="fade-in">
                        <h2 class="text-3xl font-bold mb-6 text-gray-800">我们的使命</h2>
                        <div class="space-y-4 text-gray-600">
                            <p class="text-lg">
                                🎯 <strong>培养目标：</strong>培养具备实战能力的网络安全人才，通过"以赛促学、以战代练"模式，提升成员在CTF竞赛、企业渗透测试、网络攻防演练等领域的实战能力。
                            </p>
                            <p class="text-lg">
                                🚀 <strong>发展愿景：</strong>成为西南地区知名的高校网络安全社团，为网络安全行业输送优秀人才，推动校园网络安全文化建设。
                            </p>
                            <p class="text-lg">
                                💡 <strong>核心理念：</strong>实践是检验真理的唯一标准，致力于为所有对网络世界充满好奇的同学，提供一个学习和交流的平台。
                            </p>
                        </div>
                    </div>
                    
                    <div class="fade-in">
                        <div class="bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-2xl">
                            <h3 class="text-2xl font-bold mb-6 text-center text-gray-800">社团成就</h3>
                            <div class="text-center space-y-4">
                                <div class="achievement-badge">
                                    <i class="fas fa-trophy mr-2"></i>全国高校CTF竞赛一等奖
                                </div>
                                <div class="achievement-badge">
                                    <i class="fas fa-medal mr-2"></i>四川省网络安全竞赛二等奖
                                </div>
                                <div class="achievement-badge">
                                    <i class="fas fa-star mr-2"></i>学院创新创业大赛特等奖
                                </div>
                                <div class="achievement-badge">
                                    <i class="fas fa-award mr-2"></i>优秀学生社团
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <section class="section-padding bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="section-title">核心团队</h2>
                    <p class="section-subtitle">
                        经验丰富的技术专家和充满激情的安全爱好者
                    </p>
                </div>
                
                <div id="team-container" class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 动态加载团队成员 -->
                </div>
            </div>
        </section>

        <!-- Development Timeline -->
        <section class="section-padding bg-white">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="section-title">发展历程</h2>
                    <p class="section-subtitle">
                        见证我们从初创到成长的每一个重要时刻
                    </p>
                </div>
                
                <div class="timeline max-w-4xl mx-auto">
                    <div class="timeline-item fade-in">
                        <div class="timeline-content">
                            <h3 class="text-xl font-bold text-blue-600 mb-2">2022年3月</h3>
                            <h4 class="text-lg font-semibold mb-3">社团成立</h4>
                            <p class="text-gray-600">
                                由几名网络安全爱好者发起成立，初期成员20人，开始定期举办技术分享会。
                            </p>
                        </div>
                    </div>
                    
                    <div class="timeline-item fade-in">
                        <div class="timeline-content">
                            <h3 class="text-xl font-bold text-blue-600 mb-2">2022年9月</h3>
                            <h4 class="text-lg font-semibold mb-3">首次参赛</h4>
                            <p class="text-gray-600">
                                参加四川省大学生网络安全竞赛，获得优秀奖，标志着社团正式走向竞技舞台。
                            </p>
                        </div>
                    </div>
                    
                    <div class="timeline-item fade-in">
                        <div class="timeline-content">
                            <h3 class="text-xl font-bold text-blue-600 mb-2">2023年5月</h3>
                            <h4 class="text-lg font-semibold mb-3">重大突破</h4>
                            <p class="text-gray-600">
                                在全国高校网络安全挑战赛中获得一等奖，成为学院历史上首个获得国家级奖项的技术社团。
                            </p>
                        </div>
                    </div>
                    
                    <div class="timeline-item fade-in">
                        <div class="timeline-content">
                            <h3 class="text-xl font-bold text-blue-600 mb-2">2023年11月</h3>
                            <h4 class="text-lg font-semibold mb-3">规模扩大</h4>
                            <p class="text-gray-600">
                                社团成员突破100人，建立了完善的培训体系和项目管理制度。
                            </p>
                        </div>
                    </div>
                    
                    <div class="timeline-item fade-in">
                        <div class="timeline-content">
                            <h3 class="text-xl font-bold text-blue-600 mb-2">2024年至今</h3>
                            <h4 class="text-lg font-semibold mb-3">持续发展</h4>
                            <p class="text-gray-600">
                                继续在各类竞赛中取得优异成绩，同时积极开展校园网络安全科普活动，影响力不断扩大。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Values Section -->
        <section class="section-padding bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="section-title">核心价值观</h2>
                    <p class="section-subtitle">
                        指导我们前进的基本原则和信念
                    </p>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="card text-center fade-in">
                        <div class="card-body">
                            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-lightbulb text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-bold mb-3">创新探索</h3>
                            <p class="text-gray-600 text-sm">勇于尝试新技术，探索网络安全前沿领域</p>
                        </div>
                    </div>
                    
                    <div class="card text-center fade-in" style="transition-delay: 150ms;">
                        <div class="card-body">
                            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-handshake text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-bold mb-3">团队协作</h3>
                            <p class="text-gray-600 text-sm">重视团队合作，共同成长进步</p>
                        </div>
                    </div>
                    
                    <div class="card text-center fade-in" style="transition-delay: 300ms;">
                        <div class="card-body">
                            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-bold mb-3">持续学习</h3>
                            <p class="text-gray-600 text-sm">保持学习热情，不断提升技术能力</p>
                        </div>
                    </div>
                    
                    <div class="card text-center fade-in" style="transition-delay: 450ms;">
                        <div class="card-body">
                            <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-heart text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-bold mb-3">责任担当</h3>
                            <p class="text-gray-600 text-sm">承担网络安全责任，保护数字世界</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="section-padding gradient-bg text-white">
            <div class="container mx-auto px-6 text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6 fade-in">
                    想要了解更多？
                </h2>
                <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto fade-in">
                    欢迎加入我们，一起在网络安全的世界里探索、学习、成长
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
                    <a href="join.html" class="btn btn-secondary">
                        立即加入 <i class="fas fa-rocket ml-2"></i>
                    </a>
                    <a href="projects.html" class="btn btn-outline">
                        查看项目 <i class="fas fa-code ml-2"></i>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white">
        <div class="container mx-auto px-6 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="h-10 w-10 mr-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white"></i>
                        </div>
                        <span class="font-bold text-xl">NIS网络信息安全社团</span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        成都工业职业技术学院网络信息安全社团，致力于培养网络安全人才，推动校园网络安全文化建设。
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-qq text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-weixin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-300 hover:text-white transition-colors">首页</a></li>
                        <li><a href="projects.html" class="text-gray-300 hover:text-white transition-colors">项目展示</a></li>
                        <li><a href="resources.html" class="text-gray-300 hover:text-white transition-colors">学习资源</a></li>
                        <li><a href="events.html" class="text-gray-300 hover:text-white transition-colors">活动回顾</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">联系我们</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><i class="fas fa-map-marker-alt mr-2"></i>成都工业职业技术学院</li>
                        <li><i class="fas fa-users mr-2"></i>QQ群: 242050951</li>
                        <li><i class="fab fa-tiktok mr-2"></i>抖音: 21647629167</li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 NIS网络信息安全社团. 保留所有权利.</p>
                <p class="mt-2 text-sm">Designed & Built with ❤️ by NIS Club</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/common_improved.js"></script>
    <script src="/static/js/api.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载团队成员
            window.dataRenderer.renderTeam('team-container');
        });
    </script>
</body>
</html>
