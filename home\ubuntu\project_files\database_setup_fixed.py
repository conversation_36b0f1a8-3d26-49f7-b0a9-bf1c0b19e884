#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版数据库设置 - 解决cursor未读结果问题
"""

import mysql.connector
from mysql.connector import Error
import json
import logging
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'root'),
    'charset': 'utf8mb4'
}

DATABASE_NAME = os.getenv('DB_NAME', 'nis_security_news')

def create_database():
    """创建数据库"""
    connection = None
    cursor = None
    try:
        # 连接MySQL服务器
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DATABASE_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        logger.info("✅ 数据库创建成功")
        
        return True
        
    except Error as e:
        logger.error(f"❌ 创建数据库失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def create_tables():
    """创建改进的表结构"""
    connection = None
    cursor = None
    try:
        db_config = DB_CONFIG.copy()
        db_config['database'] = DATABASE_NAME
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 新闻分类表
        categories_table = """
        CREATE TABLE IF NOT EXISTS news_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
            description TEXT COMMENT '分类描述',
            sort_order INT DEFAULT 0 COMMENT '排序',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_active_sort (is_active, sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻分类表'
        """
        
        # 2. 新闻链接表 - 增强安全性
        news_links_table = """
        CREATE TABLE IF NOT EXISTS news_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(500) NOT NULL COMMENT '文章标题',
            url VARCHAR(1000) NOT NULL COMMENT '文章链接',
            description TEXT COMMENT '文章描述',
            category_id INT COMMENT '分类ID',
            status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '状态',
            views INT DEFAULT 0 COMMENT '浏览次数',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES news_categories(id) ON DELETE SET NULL,
            INDEX idx_status_featured (status, is_featured),
            INDEX idx_category_status (category_id, status),
            INDEX idx_created_at (created_at),
            INDEX idx_views (views),
            UNIQUE INDEX idx_url_unique (url(255)),
            FULLTEXT INDEX idx_search (title, description)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻链接表'
        """

        # 3. 项目展示表 - 增强字段
        projects_table = """
        CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '项目标题',
            description TEXT COMMENT '项目描述',
            image_url VARCHAR(500) COMMENT '项目图片',
            github_url VARCHAR(500) COMMENT 'GitHub链接',
            demo_url VARCHAR(500) COMMENT '演示链接',
            technologies JSON COMMENT '使用技术',
            status ENUM('planning', 'developing', 'completed', 'archived') DEFAULT 'developing' COMMENT '项目状态',
            award VARCHAR(200) COMMENT '获奖情况',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            sort_order INT DEFAULT 0 COMMENT '排序',
            created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status_featured (status, is_featured),
            INDEX idx_sort_order (sort_order),
            FULLTEXT INDEX idx_project_search (title, description)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目展示表'
        """

        # 4. 学习资源表 - 增强分类
        resources_table = """
        CREATE TABLE IF NOT EXISTS learning_resources (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '资源标题',
            description TEXT COMMENT '资源描述',
            url VARCHAR(500) COMMENT '资源链接',
            file_path VARCHAR(500) COMMENT '文件路径',
            resource_type ENUM('link', 'file', 'video', 'document') DEFAULT 'link' COMMENT '资源类型',
            category VARCHAR(100) COMMENT '资源分类',
            difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
            tags JSON COMMENT '标签',
            download_count INT DEFAULT 0 COMMENT '下载次数',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '状态',
            created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_type_status (resource_type, status),
            INDEX idx_category_difficulty (category, difficulty),
            INDEX idx_featured_status (is_featured, status),
            INDEX idx_download_count (download_count),
            FULLTEXT INDEX idx_resource_search (title, description)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习资源表'
        """

        # 5. 活动回顾表 - 增强功能
        events_table = """
        CREATE TABLE IF NOT EXISTS events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '活动标题',
            description TEXT COMMENT '活动描述',
            content LONGTEXT COMMENT '活动详情',
            event_date DATE COMMENT '活动日期',
            start_time TIME COMMENT '开始时间',
            end_time TIME COMMENT '结束时间',
            location VARCHAR(200) COMMENT '活动地点',
            image_url VARCHAR(500) COMMENT '活动图片',
            gallery JSON COMMENT '图片集',
            participants_count INT DEFAULT 0 COMMENT '参与人数',
            max_participants INT COMMENT '最大参与人数',
            status ENUM('upcoming', 'ongoing', 'completed', 'cancelled') DEFAULT 'upcoming' COMMENT '活动状态',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            registration_required BOOLEAN DEFAULT FALSE COMMENT '是否需要报名',
            created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_date_status (event_date, status),
            INDEX idx_featured_status (is_featured, status),
            INDEX idx_registration (registration_required),
            FULLTEXT INDEX idx_event_search (title, description, content)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动回顾表'
        """

        # 6. 团队成员表 - 增强信息
        members_table = """
        CREATE TABLE IF NOT EXISTS team_members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT '姓名',
            position VARCHAR(100) COMMENT '职位',
            bio TEXT COMMENT '个人简介',
            avatar_url VARCHAR(500) COMMENT '头像链接',
            skills JSON COMMENT '技能标签',
            github_url VARCHAR(200) COMMENT 'GitHub链接',
            email VARCHAR(200) COMMENT '邮箱',
            phone VARCHAR(20) COMMENT '联系电话',
            join_date DATE COMMENT '加入日期',
            is_core BOOLEAN DEFAULT FALSE COMMENT '是否核心成员',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否在职',
            sort_order INT DEFAULT 0 COMMENT '排序',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_core_active (is_core, is_active),
            INDEX idx_sort_order (sort_order),
            INDEX idx_join_date (join_date),
            UNIQUE INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队成员表'
        """
        
        # 7. 系统配置表 - 增强配置管理
        system_config_table = """
        CREATE TABLE IF NOT EXISTS system_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
            config_value TEXT COMMENT '配置值',
            config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
            description TEXT COMMENT '配置描述',
            is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_public (is_public)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'
        """

        # 8. 公告表 - 增强功能
        announcements_table = """
        CREATE TABLE IF NOT EXISTS announcements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '公告标题',
            content TEXT NOT NULL COMMENT '公告内容',
            type ENUM('notice', 'event', 'urgent', 'maintenance') DEFAULT 'notice' COMMENT '公告类型',
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
            is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
            priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '优先级',
            publish_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
            expire_time TIMESTAMP NULL COMMENT '过期时间',
            created_by VARCHAR(100) DEFAULT 'admin' COMMENT '创建者',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status_publish (status, publish_time),
            INDEX idx_type_priority (type, priority),
            INDEX idx_pinned_status (is_pinned, status),
            INDEX idx_expire_time (expire_time),
            FULLTEXT INDEX idx_announcement_search (title, content)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表'
        """

        # 9. 操作日志表 - 新增安全审计
        audit_logs_table = """
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(100) COMMENT '用户ID',
            action VARCHAR(100) NOT NULL COMMENT '操作类型',
            table_name VARCHAR(100) COMMENT '操作表名',
            record_id INT COMMENT '记录ID',
            old_values JSON COMMENT '旧值',
            new_values JSON COMMENT '新值',
            ip_address VARCHAR(45) COMMENT 'IP地址',
            user_agent TEXT COMMENT '用户代理',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_action (user_id, action),
            INDEX idx_table_record (table_name, record_id),
            INDEX idx_created_at (created_at),
            INDEX idx_ip_address (ip_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表'
        """

        # 10. 用户会话表 - 新增会话管理
        user_sessions_table = """
        CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(128) PRIMARY KEY COMMENT '会话ID',
            user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
            ip_address VARCHAR(45) COMMENT 'IP地址',
            user_agent TEXT COMMENT '用户代理',
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
            expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
            INDEX idx_user_active (user_id, is_active),
            INDEX idx_expires_at (expires_at),
            INDEX idx_ip_address (ip_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表'
        """
        
        # 执行创建表的SQL
        tables = [
            ("新闻分类表", categories_table),
            ("新闻链接表", news_links_table),
            ("项目展示表", projects_table),
            ("学习资源表", resources_table),
            ("活动回顾表", events_table),
            ("团队成员表", members_table),
            ("系统配置表", system_config_table),
            ("公告表", announcements_table),
            ("操作日志表", audit_logs_table),
            ("用户会话表", user_sessions_table)
        ]
        
        for table_name, table_sql in tables:
            cursor.execute(table_sql)
            logger.info(f"✅ {table_name}创建成功")
        
        connection.commit()
        return True
        
    except Error as e:
        logger.error(f"❌ 创建表失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def insert_default_data():
    """插入默认数据"""
    connection = None
    cursor = None
    try:
        db_config = DB_CONFIG.copy()
        db_config['database'] = DATABASE_NAME
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 插入默认分类
        categories = [
            ('网络安全', '网络安全相关新闻', 1),
            ('漏洞情报', '安全漏洞和威胁情报', 2),
            ('技术分析', '安全技术分析文章', 3),
            ('行业动态', '安全行业动态新闻', 4),
            ('工具资源', '安全工具和资源', 5)
        ]
        
        for name, desc, sort_order in categories:
            cursor.execute("""
                INSERT IGNORE INTO news_categories (name, description, sort_order) 
                VALUES (%s, %s, %s)
            """, (name, desc, sort_order))
        
        # 插入示例新闻链接
        sample_links = [
            ('FreeBuf安全资讯', 'https://www.freebuf.com/', '国内知名网络安全媒体平台', 1, 'active', True),
            ('安全客', 'https://www.anquanke.com/', '专业的网络安全技术媒体', 1, 'active', True),
            ('Seebug漏洞平台', 'https://www.seebug.org/', '专业的漏洞情报平台', 2, 'active', True),
            ('CNVD国家信息安全漏洞库', 'https://www.cnvd.org.cn/', '国家级漏洞信息发布平台', 2, 'active', False),
            ('Kali Linux官网', 'https://www.kali.org/', '渗透测试专用Linux发行版', 5, 'active', False)
        ]

        for title, url, desc, category_id, status, is_featured in sample_links:
            cursor.execute("""
                INSERT IGNORE INTO news_links (title, url, description, category_id, status, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (title, url, desc, category_id, status, is_featured))

        # 插入示例项目
        sample_projects = [
            ('校园网络监控平台', '基于Python和ELK Stack的校园网络流量监控与异常检测平台',
             '/static/images/project1.jpg', 'https://github.com/nis/network-monitor',
             'http://demo.nis.edu.cn/monitor', '["Python", "Elasticsearch", "Flask", "Docker"]',
             'completed', '获2023年四川省大学生网络安全竞赛二等奖', True),
            ('Web安全扫描器', '自主开发的Web应用安全漏洞扫描工具',
             '/static/images/project2.jpg', 'https://github.com/nis/web-scanner',
             '', '["Python", "Requests", "BeautifulSoup", "SQLite"]',
             'completed', '获校级创新创业大赛一等奖', True),
            ('密码学实验平台', '用于密码学教学的在线实验平台',
             '/static/images/project3.jpg', 'https://github.com/nis/crypto-lab',
             'http://crypto.nis.edu.cn', '["Vue.js", "Node.js", "MySQL", "WebCrypto"]',
             'developing', '', False)
        ]

        for title, desc, img, github, demo, tech, status, award, featured in sample_projects:
            cursor.execute("""
                INSERT IGNORE INTO projects (title, description, image_url, github_url, demo_url,
                                           technologies, status, award, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, desc, img, github, demo, tech, status, award, featured))

        # 插入示例学习资源
        sample_resources = [
            ('网络安全入门指南', '适合初学者的网络安全学习路线图',
             'https://github.com/nis/security-guide', '', 'link', '入门教程', 'beginner',
             '["网络安全", "入门", "学习路线"]', 'active', True),
            ('渗透测试工具集', '常用渗透测试工具的使用教程合集',
             '', '/static/files/pentest-tools.pdf', 'file', '工具教程', 'intermediate',
             '["渗透测试", "工具", "教程"]', 'active', True),
            ('密码学基础视频', '密码学基础知识讲解视频',
             'https://www.bilibili.com/video/BV1xx411c7mu', '', 'video', '理论基础', 'beginner',
             '["密码学", "视频", "基础"]', 'active', False)
        ]

        for title, desc, url, file_path, res_type, category, difficulty, tags, status, featured in sample_resources:
            cursor.execute("""
                INSERT IGNORE INTO learning_resources (title, description, url, file_path,
                                                     resource_type, category, difficulty, tags, status, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, desc, url, file_path, res_type, category, difficulty, tags, status, featured))

        # 插入示例活动
        sample_events = [
            ('2024年网络安全技能大赛', '社团年度网络安全技能竞赛活动',
             '本次大赛包含Web安全、逆向工程、密码学等多个方向的挑战题目，旨在提升成员的实战技能。',
             '2024-03-15', '09:00:00', '17:00:00', '学校机房A301', '/static/images/event1.jpg',
             '["event1_1.jpg", "event1_2.jpg", "event1_3.jpg"]', 45, 50, 'completed', True, True),
            ('企业安全专家讲座', '邀请知名企业安全专家分享实战经验',
             '本次讲座邀请了腾讯安全团队的资深专家，分享企业级安全防护的最佳实践。',
             '2024-04-20', '14:00:00', '16:00:00', '学术报告厅', '/static/images/event2.jpg',
             '["event2_1.jpg", "event2_2.jpg"]', 120, 150, 'completed', True, False)
        ]

        for title, desc, content, date, start_time, end_time, location, img, gallery, participants, max_participants, status, featured, registration in sample_events:
            cursor.execute("""
                INSERT IGNORE INTO events (title, description, content, event_date, start_time, end_time, location,
                                         image_url, gallery, participants_count, max_participants, status, is_featured, registration_required)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, desc, content, date, start_time, end_time, location, img, gallery, participants, max_participants, status, featured, registration))

        # 插入示例团队成员
        sample_members = [
            ('张三', '社团主席', '计算机科学与技术专业，专注于Web安全和渗透测试',
             '/static/images/member1.jpg', '["Python", "Web安全", "渗透测试", "Linux"]',
             'https://github.com/zhangsan', '<EMAIL>', '13800138001', '2022-09-01', True, True, 1),
            ('李四', '技术副主席', '网络工程专业，擅长网络安全和系统管理',
             '/static/images/member2.jpg', '["网络安全", "系统管理", "防火墙", "IDS"]',
             'https://github.com/lisi', '<EMAIL>', '13800138002', '2022-09-01', True, True, 2),
            ('王五', '项目经理', '信息安全专业，负责社团项目开发和管理',
             '/static/images/member3.jpg', '["项目管理", "Java", "数据库", "DevOps"]',
             'https://github.com/wangwu', '<EMAIL>', '13800138003', '2023-03-01', True, True, 3)
        ]

        for name, position, bio, avatar, skills, github, email, phone, join_date, is_core, is_active, sort_order in sample_members:
            cursor.execute("""
                INSERT IGNORE INTO team_members (name, position, bio, avatar_url, skills,
                                               github_url, email, phone, join_date, is_core, is_active, sort_order)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (name, position, bio, avatar, skills, github, email, phone, join_date, is_core, is_active, sort_order))
        
        # 插入系统配置
        configs = [
            ('site_name', 'NIS社团安全新闻', 'string', '网站名称', True),
            ('admin_email', '<EMAIL>', 'string', '管理员邮箱', False),
            ('max_links_per_page', '20', 'number', '每页显示链接数', False),
            ('enable_featured', 'true', 'boolean', '是否启用推荐功能', False),
            ('site_description', 'NIS网络信息安全社团官方网站', 'string', '网站描述', True),
            ('contact_qq', '123456789', 'string', '联系QQ', True),
            ('contact_douyin', 'nis_security', 'string', '抖音号', True)
        ]
        
        for key, value, config_type, desc, is_public in configs:
            cursor.execute("""
                INSERT IGNORE INTO system_config (config_key, config_value, config_type, description, is_public) 
                VALUES (%s, %s, %s, %s, %s)
            """, (key, value, config_type, desc, is_public))
        
        # 插入示例公告
        sample_announcements = [
            ('欢迎使用NIS社团安全新闻系统', '这是一个改进的安全新闻链接管理系统，具有增强的安全性和功能。', 
             'notice', 'published', True, 'high', None),
            ('2024年春季招新开始', '我们正在寻找对网络安全感兴趣的同学加入我们的团队！', 
             'event', 'published', True, 'medium', '2024-12-31 23:59:59')
        ]
        
        for title, content, ann_type, status, is_pinned, priority, expire_time in sample_announcements:
            cursor.execute("""
                INSERT IGNORE INTO announcements (title, content, type, status, is_pinned, priority, expire_time) 
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (title, content, ann_type, status, is_pinned, priority, expire_time))
        
        connection.commit()
        logger.info("✅ 默认数据插入成功")
        return True
        
    except Error as e:
        logger.error(f"❌ 插入默认数据失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def main():
    """主函数"""
    logger.info("🚀 开始初始化修复版数据库...")
    logger.info("=" * 50)
    
    # 1. 创建数据库
    if not create_database():
        logger.error("❌ 数据库创建失败，停止初始化")
        return False
    
    # 2. 创建表
    if not create_tables():
        logger.error("❌ 表创建失败，停止初始化")
        return False
    
    # 3. 插入默认数据
    if not insert_default_data():
        logger.error("❌ 默认数据插入失败")
        return False
    
    logger.info("=" * 50)
    logger.info("🎉 修复版数据库初始化完成！")
    logger.info("\n📊 数据库结构:")
    logger.info("  - news_categories: 新闻分类")
    logger.info("  - news_links: 新闻链接")
    logger.info("  - projects: 项目展示")
    logger.info("  - learning_resources: 学习资源")
    logger.info("  - events: 活动管理")
    logger.info("  - team_members: 团队成员")
    logger.info("  - system_config: 系统配置")
    logger.info("  - announcements: 公告管理")
    logger.info("  - audit_logs: 操作日志")
    logger.info("  - user_sessions: 用户会话")
    logger.info("\n🔒 安全增强:")
    logger.info("  - 增强的输入验证和SQL注入防护")
    logger.info("  - 操作日志记录和审计")
    logger.info("  - 会话管理和安全控制")
    logger.info("  - 数据完整性约束")
    logger.info("\n🔗 主要功能:")
    logger.info("  - 管理员可在后台安全地管理内容")
    logger.info("  - 前端显示链接预览和跳转")
    logger.info("  - 支持分类管理和全文搜索")
    logger.info("  - 完整的内容管理系统")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

