<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全资讯 - NIS网络信息安全社团</title>
    <meta name="description" content="最新网络安全资讯、漏洞情报、技术分析和行业动态">
    <meta name="keywords" content="网络安全,安全资讯,漏洞情报,技术分析,行业动态">
    
    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style_improved.css">
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white/90 backdrop-blur-sm shadow-sm sticky top-0 z-50 transition-all duration-300">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shield-alt text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-800">NIS安全社团</h1>
                    <p class="text-xs text-gray-600">网络信息安全社团</p>
                </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-600 hover:text-blue-600 transition-colors">首页</a>
                <a href="security_news.html" class="text-blue-600 font-semibold">安全资讯</a>
                <a href="projects.html" class="text-gray-600 hover:text-blue-600 transition-colors">项目展示</a>
                <a href="resources.html" class="text-gray-600 hover:text-blue-600 transition-colors">学习资源</a>
                <a href="events.html" class="text-gray-600 hover:text-blue-600 transition-colors">活动回顾</a>
                <a href="about.html" class="text-gray-600 hover:text-blue-600 transition-colors">关于我们</a>
            </div>
            
            <div class="md:hidden">
                <button class="text-gray-600 hover:text-blue-600">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div class="container mx-auto px-6 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                安全资讯中心
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90">
                获取最新的网络安全资讯、漏洞情报和技术分析
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-newspaper mr-2"></i>
                    实时资讯
                </div>
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-bug mr-2"></i>
                    漏洞情报
                </div>
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-chart-line mr-2"></i>
                    技术分析
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filter -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="search-input" placeholder="搜索安全资讯..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>
                <div class="flex gap-2">
                    <select id="category-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="all">所有分类</option>
                        <option value="网络安全">网络安全</option>
                        <option value="漏洞情报">漏洞情报</option>
                        <option value="技术分析">技术分析</option>
                        <option value="行业动态">行业动态</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- News Grid -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div id="news-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 新闻卡片将通过JavaScript动态加载 -->
            </div>
            
            <!-- Loading -->
            <div id="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-2 text-gray-600">加载中...</p>
            </div>
            
            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button id="load-more" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors hidden">
                    加载更多
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">NIS安全社团</h3>
                            <p class="text-gray-400">网络信息安全社团</p>
                        </div>
                    </div>
                    <p class="text-gray-400 mb-6">
                        致力于网络安全技术研究与实践，培养优秀的网络安全人才，为网络空间安全贡献力量。
                    </p>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">快速链接</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">首页</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">关于我们</a></li>
                        <li><a href="projects.html" class="text-gray-400 hover:text-white transition-colors">项目展示</a></li>
                        <li><a href="resources.html" class="text-gray-400 hover:text-white transition-colors">学习资源</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li class="text-gray-400">
                            <i class="fas fa-envelope mr-2"></i>
                            <EMAIL>
                        </li>
                        <li class="text-gray-400">
                            <i class="fab fa-qq mr-2"></i>
                            QQ群: 123456789
                        </li>
                        <li class="text-gray-400">
                            <i class="fab fa-github mr-2"></i>
                            GitHub: NIS-Security
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 NIS网络信息安全社团. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/common_improved.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/security_news.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化安全资讯页面
            if (typeof SecurityNewsManager !== 'undefined') {
                new SecurityNewsManager();
            } else {
                // 如果security_news.js未加载，使用基本功能
                loadNewsLinks();
            }
        });

        // 基本的新闻加载功能
        function loadNewsLinks() {
            const container = document.getElementById('news-container');
            const loading = document.getElementById('loading');
            
            // 调用API获取新闻链接
            fetch('/api/news-links')
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    if (data.success && data.data) {
                        renderNewsCards(data.data);
                    } else {
                        container.innerHTML = '<div class="col-span-full text-center text-gray-500">暂无安全资讯</div>';
                    }
                })
                .catch(error => {
                    console.error('加载新闻失败:', error);
                    loading.style.display = 'none';
                    container.innerHTML = '<div class="col-span-full text-center text-red-500">加载失败，请稍后重试</div>';
                });
        }

        // 渲染新闻卡片
        function renderNewsCards(newsData) {
            const container = document.getElementById('news-container');
            container.innerHTML = newsData.map(news => `
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            ${news.category || '网络安全'}
                        </span>
                        <span class="text-gray-500 text-sm">${formatDate(news.created_at)}</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-3 line-clamp-2">${news.title}</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">${news.description || '暂无描述'}</p>
                    <div class="flex items-center justify-between">
                        <a href="${news.url}" target="_blank" 
                           class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                            阅读全文 <i class="fas fa-external-link-alt ml-1 text-sm"></i>
                        </a>
                        <span class="text-gray-400 text-sm">
                            <i class="fas fa-eye mr-1"></i>${news.views || 0}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }
    </script>
</body>
</html>
