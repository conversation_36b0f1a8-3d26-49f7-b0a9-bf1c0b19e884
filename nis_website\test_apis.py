#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_api_endpoint(url, description):
    """测试API端点"""
    try:
        print(f"🔍 测试: {description}")
        print(f"   URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, dict) and data.get('success'):
                    print(f"   ✅ 成功 - 返回数据: {len(data.get('data', []))} 条记录")
                else:
                    print(f"   ⚠️  响应格式异常: {str(data)[:100]}...")
            except json.JSONDecodeError:
                print(f"   ⚠️  非JSON响应: {response.text[:100]}...")
        else:
            print(f"   ❌ 失败 - {response.text[:100]}...")
        
        print()
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 请求失败: {e}")
        print()
        return False

def test_static_files():
    """测试静态文件"""
    base_url = "http://127.0.0.1:5000"
    
    static_files = [
        ("/", "首页"),
        ("/index.html", "首页HTML"),
        ("/security_news.html", "安全资讯页面"),
        ("/projects.html", "项目展示页面"),
        ("/resources.html", "学习资源页面"),
        ("/events.html", "活动回顾页面"),
        ("/about.html", "关于我们页面"),
        ("/announcements.html", "公告页面"),
        ("/join.html", "加入我们页面"),
        ("/admin_simple.html", "管理后台"),
        ("/static/css/style_improved.css", "CSS样式文件"),
        ("/static/js/common_improved.js", "通用JS文件"),
        ("/static/js/api.js", "API JS文件"),
        ("/static/js/security_news.js", "安全资讯JS文件"),
    ]
    
    print("🌐 测试静态文件访问...")
    print("=" * 60)
    
    success_count = 0
    for path, description in static_files:
        url = base_url + path
        if test_api_endpoint(url, description):
            success_count += 1
        time.sleep(0.1)  # 避免请求过快
    
    print(f"静态文件测试结果: {success_count}/{len(static_files)} 成功")
    print()

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://127.0.0.1:5000/api"
    
    api_endpoints = [
        ("/announcements", "公告列表"),
        ("/projects", "项目列表"),
        ("/news-links", "新闻链接"),
        ("/resources", "学习资源"),
        ("/categories", "新闻分类"),
        ("/team", "团队成员"),
        ("/events", "活动列表"),
        ("/security-news?page=1&category=all&search=&limit=10", "安全资讯搜索"),
        ("/security-news?page=1&category=网络安全&search=&limit=5", "分类过滤"),
        ("/security-news?page=1&category=all&search=安全&limit=5", "关键词搜索"),
    ]
    
    print("🔌 测试API端点...")
    print("=" * 60)
    
    success_count = 0
    for path, description in api_endpoints:
        url = base_url + path
        if test_api_endpoint(url, description):
            success_count += 1
        time.sleep(0.1)  # 避免请求过快
    
    print(f"API测试结果: {success_count}/{len(api_endpoints)} 成功")
    print()

def test_search_functionality():
    """测试搜索功能"""
    base_url = "http://127.0.0.1:5000/api"
    
    print("🔍 测试搜索功能...")
    print("=" * 60)
    
    search_tests = [
        ("security-news?search=FreeBuf", "搜索FreeBuf"),
        ("security-news?search=安全", "搜索中文关键词"),
        ("security-news?category=网络安全", "按分类过滤"),
        ("security-news?page=2&limit=3", "分页测试"),
    ]
    
    success_count = 0
    for path, description in search_tests:
        url = base_url + "/" + path
        if test_api_endpoint(url, description):
            success_count += 1
        time.sleep(0.1)
    
    print(f"搜索功能测试结果: {success_count}/{len(search_tests)} 成功")
    print()

def main():
    """主测试函数"""
    print("🚀 开始全面测试NIS网站...")
    print("=" * 60)
    print()
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试静态文件
    test_static_files()
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试搜索功能
    test_search_functionality()
    
    print("🎉 测试完成!")
    print("=" * 60)
    print()
    print("📋 测试总结:")
    print("   - 如果所有测试都通过，网站功能正常")
    print("   - 如果有失败项，请检查对应的功能")
    print("   - 可以在浏览器中访问 http://127.0.0.1:5000 进行手动测试")

if __name__ == "__main__":
    main()
