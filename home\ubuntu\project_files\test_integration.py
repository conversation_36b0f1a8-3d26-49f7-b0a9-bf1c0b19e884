#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIS网络信息安全社团网站 - 集成测试脚本
测试所有API端点和功能
"""

import requests
import json
import time
import sys
from datetime import datetime

# 测试配置
BASE_URL = 'http://127.0.0.1:5000'
ADMIN_USERNAME = 'admin'
ADMIN_PASSWORD = 'nis2024'

class TestRunner:
    def __init__(self):
        self.session = requests.Session()
        self.csrf_token = None
        self.test_results = []
        
    def log(self, message, level='INFO'):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_api_endpoint(self, method, endpoint, data=None, expected_status=200, description=""):
        """测试API端点"""
        try:
            url = f"{BASE_URL}{endpoint}"
            
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                headers = {'Content-Type': 'application/json'}
                if self.csrf_token:
                    headers['X-CSRF-Token'] = self.csrf_token
                    if data:
                        data['csrf_token'] = self.csrf_token
                response = self.session.post(url, json=data, headers=headers)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            success = response.status_code == expected_status
            result = {
                'endpoint': endpoint,
                'method': method,
                'status_code': response.status_code,
                'expected_status': expected_status,
                'success': success,
                'description': description,
                'response_time': response.elapsed.total_seconds()
            }
            
            if success:
                self.log(f"✅ {method} {endpoint} - {description}", 'PASS')
                try:
                    result['response_data'] = response.json()
                except:
                    result['response_data'] = response.text[:200]
            else:
                self.log(f"❌ {method} {endpoint} - {description} (状态码: {response.status_code})", 'FAIL')
                result['error'] = response.text[:200]
            
            self.test_results.append(result)
            return response
            
        except Exception as e:
            self.log(f"❌ {method} {endpoint} - {description} (异常: {str(e)})", 'ERROR')
            self.test_results.append({
                'endpoint': endpoint,
                'method': method,
                'success': False,
                'description': description,
                'error': str(e)
            })
            return None
    
    def get_csrf_token(self):
        """获取CSRF令牌"""
        try:
            response = self.test_api_endpoint('GET', '/api/csrf-token', description="获取CSRF令牌")
            if response and response.status_code == 200:
                data = response.json()
                self.csrf_token = data.get('csrf_token')
                self.log(f"获取CSRF令牌成功: {self.csrf_token[:20]}...")
                return True
        except Exception as e:
            self.log(f"获取CSRF令牌失败: {e}", 'ERROR')
        return False
    
    def test_admin_login(self):
        """测试管理员登录"""
        login_data = {
            'username': ADMIN_USERNAME,
            'password': ADMIN_PASSWORD
        }
        
        response = self.test_api_endpoint(
            'POST', '/api/admin/login', 
            data=login_data, 
            description="管理员登录"
        )
        
        if response and response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    self.log("管理员登录成功", 'PASS')
                    return True
            except:
                pass
        
        self.log("管理员登录失败", 'FAIL')
        return False
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("开始集成测试...")
        self.log("=" * 60)
        
        # 1. 测试基础API端点
        self.log("测试基础API端点...")
        
        # 健康检查
        self.test_api_endpoint('GET', '/health', description="健康检查")
        
        # 获取CSRF令牌
        self.get_csrf_token()
        
        # 公告API
        self.test_api_endpoint('GET', '/api/announcements', description="获取公告列表")
        
        # 新闻链接API
        self.test_api_endpoint('GET', '/api/news-links', description="获取新闻链接")
        
        # 分类API
        self.test_api_endpoint('GET', '/api/categories', description="获取分类")
        
        # 安全新闻API
        self.test_api_endpoint('GET', '/api/security-news', description="获取安全新闻")
        
        # 项目API
        self.test_api_endpoint('GET', '/api/projects', description="获取项目列表")
        
        # 团队API
        self.test_api_endpoint('GET', '/api/team', description="获取团队信息")
        
        # 资源API
        self.test_api_endpoint('GET', '/api/resources', description="获取学习资源")
        
        # 活动API
        self.test_api_endpoint('GET', '/api/events', description="获取活动列表")
        
        # 2. 测试管理员功能
        self.log("\n测试管理员功能...")
        
        # 管理员登录
        if self.test_admin_login():
            # 测试管理员API（需要登录）
            self.test_api_endpoint('GET', '/api/admin/announcements', 
                                 expected_status=401, description="管理员公告API（未实现完整认证）")
        
        # 3. 测试错误处理
        self.log("\n测试错误处理...")
        
        # 不存在的端点
        self.test_api_endpoint('GET', '/api/nonexistent', 
                             expected_status=404, description="不存在的API端点")
        
        # 无效的公告ID
        self.test_api_endpoint('GET', '/api/announcements/99999', 
                             expected_status=404, description="无效的公告ID")
        
        # 4. 测试性能
        self.log("\n测试性能...")
        start_time = time.time()
        
        for i in range(5):
            self.test_api_endpoint('GET', '/api/announcements', description=f"性能测试 {i+1}/5")
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 5
        self.log(f"平均响应时间: {avg_time:.3f}秒")
        
        # 5. 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        self.log("\n" + "=" * 60)
        self.log("测试报告")
        self.log("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.get('success', False))
        failed_tests = total_tests - passed_tests
        
        self.log(f"总测试数: {total_tests}")
        self.log(f"通过: {passed_tests}")
        self.log(f"失败: {failed_tests}")
        self.log(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            self.log("\n失败的测试:")
            for result in self.test_results:
                if not result.get('success', False):
                    self.log(f"  ❌ {result['method']} {result['endpoint']} - {result['description']}")
                    if 'error' in result:
                        self.log(f"     错误: {result['error']}")
        
        # 保存详细报告到文件
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'success_rate': passed_tests/total_tests*100
                },
                'results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        self.log(f"\n详细报告已保存到: {report_file}")
        
        return passed_tests == total_tests

def main():
    """主函数"""
    print("NIS网络信息安全社团网站 - 集成测试")
    print("=" * 60)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"✅ 服务器运行正常 (状态码: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保Flask应用正在运行在 http://127.0.0.1:5000")
        sys.exit(1)
    
    # 运行测试
    runner = TestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n⚠️ 部分测试失败，请检查报告")
        sys.exit(1)

if __name__ == '__main__':
    main()

