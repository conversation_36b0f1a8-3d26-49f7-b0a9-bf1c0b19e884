#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from mysql.connector import Error

def check_database():
    """检查数据库状态和数据"""
    connection = None
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            database='nis_website',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            print("🔍 检查数据库状态...")
            print("=" * 50)
            
            # 检查表是否存在
            tables = [
                'news_categories',
                'news_links', 
                'announcements',
                'projects',
                'learning_resources',
                'team_members',
                'events'
            ]
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"✅ {table}: {count} 条记录")
                except Error as e:
                    print(f"❌ {table}: 表不存在或查询失败 - {e}")
            
            print("\n" + "=" * 50)
            print("📊 详细数据检查...")
            
            # 检查新闻分类
            print("\n🏷️ 新闻分类:")
            cursor.execute("SELECT id, name, description FROM news_categories LIMIT 5")
            categories = cursor.fetchall()
            for cat in categories:
                print(f"  - ID:{cat[0]} 名称:{cat[1]} 描述:{cat[2]}")
            
            # 检查新闻链接
            print("\n📰 新闻链接:")
            cursor.execute("SELECT id, title, url, status FROM news_links LIMIT 5")
            links = cursor.fetchall()
            for link in links:
                print(f"  - ID:{link[0]} 标题:{link[1][:30]}... 状态:{link[3]}")
            
            # 检查公告
            print("\n📢 公告:")
            cursor.execute("SELECT id, title, type, status FROM announcements LIMIT 5")
            announcements = cursor.fetchall()
            for ann in announcements:
                print(f"  - ID:{ann[0]} 标题:{ann[1][:30]}... 类型:{ann[2]} 状态:{ann[3]}")
            
            # 检查项目
            print("\n🚀 项目:")
            cursor.execute("SELECT id, title, status FROM projects LIMIT 5")
            projects = cursor.fetchall()
            for proj in projects:
                print(f"  - ID:{proj[0]} 标题:{proj[1][:30]}... 状态:{proj[2]}")
            
            print("\n✅ 数据库检查完成!")
            
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    check_database()
