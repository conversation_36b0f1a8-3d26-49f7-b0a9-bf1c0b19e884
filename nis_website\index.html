<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NIS网络信息安全社团 - 首页</title>
    <link rel="stylesheet" href="/static/css/style_improved.css">
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <meta name="description" content="NIS网络信息安全社团官方网站，提供网络安全资讯、技术分享、项目展示和学习资源">
    <meta name="keywords" content="网络安全,信息安全,NIS社团,安全技术,渗透测试,CTF">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="/static/images/logo.png" alt="NIS Logo" class="logo-img">
                <span class="logo-text">NIS安全社团</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">首页</a>
                </li>
                <li class="nav-item">
                    <a href="security_news.html" class="nav-link">安全资讯</a>
                </li>
                <li class="nav-item">
                    <a href="projects.html" class="nav-link">项目展示</a>
                </li>
                <li class="nav-item">
                    <a href="resources.html" class="nav-link">学习资源</a>
                </li>
                <li class="nav-item">
                    <a href="events.html" class="nav-link">活动回顾</a>
                </li>
                <li class="nav-item">
                    <a href="about.html" class="nav-link">关于我们</a>
                </li>
                <li class="nav-item">
                    <a href="join.html" class="nav-link join-btn">加入我们</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 英雄区域 -->
        <section class="hero">
            <div class="hero-background">
                <div class="hero-overlay"></div>
                <video autoplay muted loop class="hero-video">
                    <source src="/static/videos/cyber-bg.mp4" type="video/mp4">
                </video>
            </div>
            <div class="hero-content">
                <div class="container">
                    <h1 class="hero-title">
                        <span class="title-line">NIS网络信息安全社团</span>
                        <span class="title-line">守护数字世界的安全</span>
                    </h1>
                    <p class="hero-subtitle">
                        专注于网络安全技术研究、人才培养和实战演练<br>
                        致力于构建更安全的网络环境
                    </p>
                    <div class="hero-buttons">
                        <a href="security_news.html" class="btn btn-primary">
                            <span>探索安全资讯</span>
                            <i class="icon-arrow-right"></i>
                        </a>
                        <a href="join.html" class="btn btn-secondary">
                            <span>加入我们</span>
                            <i class="icon-users"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="hero-scroll-indicator">
                <div class="scroll-arrow"></div>
            </div>
        </section>

        <!-- 特色功能 -->
        <section class="features">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">我们的特色</h2>
                    <p class="section-subtitle">专业的网络安全技术团队，提供全方位的安全服务</p>
                </div>
                <div class="features-grid">
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-icon">
                            <i class="icon-shield"></i>
                        </div>
                        <h3 class="feature-title">安全研究</h3>
                        <p class="feature-description">
                            深入研究最新的网络安全威胁和防护技术，
                            为社区提供专业的安全分析和解决方案。
                        </p>
                    </div>
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-icon">
                            <i class="icon-code"></i>
                        </div>
                        <h3 class="feature-title">技术开发</h3>
                        <p class="feature-description">
                            开发各类安全工具和平台，
                            包括漏洞扫描器、监控系统等实用工具。
                        </p>
                    </div>
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-icon">
                            <i class="icon-users"></i>
                        </div>
                        <h3 class="feature-title">人才培养</h3>
                        <p class="feature-description">
                            通过培训、竞赛和实战演练，
                            培养优秀的网络安全专业人才。
                        </p>
                    </div>
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-icon">
                            <i class="icon-trophy"></i>
                        </div>
                        <h3 class="feature-title">竞赛参与</h3>
                        <p class="feature-description">
                            积极参与各类网络安全竞赛，
                            在CTF、攻防演练等比赛中屡获佳绩。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 最新公告 -->
        <section class="announcements">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">最新公告</h2>
                    <a href="announcements.html" class="view-all-link">查看全部</a>
                </div>
                <div class="announcements-container">
                    <div class="announcements-list" id="announcements-list">
                        <!-- 公告内容将通过JavaScript动态加载 -->
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                            <p>加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 热门项目 -->
        <section class="projects-preview">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">精选项目</h2>
                    <a href="projects.html" class="view-all-link">查看全部</a>
                </div>
                <div class="projects-grid" id="projects-grid">
                    <!-- 项目内容将通过JavaScript动态加载 -->
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计数据 -->
        <section class="stats">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-number" data-target="50">0</div>
                        <div class="stat-label">活跃成员</div>
                    </div>
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-number" data-target="15">0</div>
                        <div class="stat-label">完成项目</div>
                    </div>
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-number" data-target="8">0</div>
                        <div class="stat-label">获奖次数</div>
                    </div>
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-number" data-target="100">0</div>
                        <div class="stat-label">技术分享</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系我们 -->
        <section class="contact">
            <div class="container">
                <div class="contact-content">
                    <div class="contact-info">
                        <h2 class="contact-title">联系我们</h2>
                        <p class="contact-description">
                            有任何问题或建议，欢迎随时联系我们。
                            我们期待与您的交流与合作。
                        </p>
                        <div class="contact-methods">
                            <div class="contact-method">
                                <i class="icon-mail"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-method">
                                <i class="icon-message-circle"></i>
                                <span>QQ群：123456789</span>
                            </div>
                            <div class="contact-method">
                                <i class="icon-video"></i>
                                <span>抖音：nis_security</span>
                            </div>
                        </div>
                    </div>
                    <div class="contact-form">
                        <form id="contact-form">
                            <div class="form-group">
                                <input type="text" id="name" name="name" placeholder="您的姓名" required>
                            </div>
                            <div class="form-group">
                                <input type="email" id="email" name="email" placeholder="您的邮箱" required>
                            </div>
                            <div class="form-group">
                                <textarea id="message" name="message" placeholder="您的留言" rows="4" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <span>发送消息</span>
                                <i class="icon-send"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="/static/images/logo.png" alt="NIS Logo" class="logo-img">
                        <span class="logo-text">NIS安全社团</span>
                    </div>
                    <p class="footer-description">
                        专注于网络安全技术研究与人才培养，
                        致力于构建更安全的网络环境。
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link" title="GitHub">
                            <i class="icon-github"></i>
                        </a>
                        <a href="#" class="social-link" title="QQ群">
                            <i class="icon-message-circle"></i>
                        </a>
                        <a href="#" class="social-link" title="抖音">
                            <i class="icon-video"></i>
                        </a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3 class="footer-title">快速链接</h3>
                    <ul class="footer-links">
                        <li><a href="security_news.html">安全资讯</a></li>
                        <li><a href="projects.html">项目展示</a></li>
                        <li><a href="resources.html">学习资源</a></li>
                        <li><a href="events.html">活动回顾</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 class="footer-title">关于我们</h3>
                    <ul class="footer-links">
                        <li><a href="about.html">团队介绍</a></li>
                        <li><a href="join.html">加入我们</a></li>
                        <li><a href="announcements.html">公告通知</a></li>
                        <li><a href="admin_simple.html">管理后台</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 class="footer-title">联系方式</h3>
                    <div class="contact-info">
                        <p><i class="icon-mail"></i> <EMAIL></p>
                        <p><i class="icon-message-circle"></i> QQ群：123456789</p>
                        <p><i class="icon-video"></i> 抖音：nis_security</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 NIS网络信息安全社团. 保留所有权利.</p>
                <p>本网站仅供学习交流使用</p>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="back-to-top" title="返回顶部">
        <i class="icon-arrow-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="/static/js/common_improved.js"></script>
    <script src="/static/js/api.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化导航栏
            initNavigation();
            
            // 初始化滚动效果
            initScrollEffects();
            
            // 加载公告数据
            loadAnnouncements();
            
            // 加载项目数据
            loadProjects();
            
            // 初始化统计数字动画
            initStatsAnimation();
            
            // 初始化联系表单
            initContactForm();
            
            // 初始化返回顶部按钮
            initBackToTop();
        });

        // 加载公告数据
        async function loadAnnouncements() {
            try {
                const response = await fetch('/api/announcements');
                const data = await response.json();
                
                if (data.success && data.data.length > 0) {
                    renderAnnouncements(data.data.slice(0, 3)); // 只显示前3条
                } else {
                    showEmptyState('announcements-list', '暂无公告');
                }
            } catch (error) {
                console.error('加载公告失败:', error);
                showErrorState('announcements-list', '加载公告失败');
            }
        }

        // 渲染公告列表
        function renderAnnouncements(announcements) {
            const container = document.getElementById('announcements-list');
            container.innerHTML = announcements.map(announcement => `
                <div class="announcement-card" data-aos="fade-up">
                    <div class="announcement-header">
                        <span class="announcement-type ${announcement.type}">${getAnnouncementTypeText(announcement.type)}</span>
                        <span class="announcement-date">${formatDate(announcement.publish_time)}</span>
                    </div>
                    <h3 class="announcement-title">${escapeHtml(announcement.title)}</h3>
                    <p class="announcement-content">${escapeHtml(announcement.content.substring(0, 100))}...</p>
                    <a href="announcements.html#announcement-${announcement.id}" class="read-more">阅读更多</a>
                </div>
            `).join('');
        }

        // 加载项目数据
        async function loadProjects() {
            try {
                const response = await fetch('/api/projects');
                const data = await response.json();
                
                if (data.success && data.data.length > 0) {
                    renderProjects(data.data.filter(p => p.is_featured).slice(0, 3)); // 只显示推荐的前3个
                } else {
                    showEmptyState('projects-grid', '暂无项目');
                }
            } catch (error) {
                console.error('加载项目失败:', error);
                showErrorState('projects-grid', '加载项目失败');
            }
        }

        // 渲染项目列表
        function renderProjects(projects) {
            const container = document.getElementById('projects-grid');
            container.innerHTML = projects.map(project => `
                <div class="project-card" data-aos="fade-up">
                    <div class="project-image">
                        <img src="${project.image_url || '/static/images/project-default.jpg'}" alt="${escapeHtml(project.title)}" loading="lazy">
                        <div class="project-overlay">
                            <div class="project-links">
                                ${project.github_url ? `<a href="${project.github_url}" target="_blank" class="project-link" title="GitHub"><i class="icon-github"></i></a>` : ''}
                                ${project.demo_url ? `<a href="${project.demo_url}" target="_blank" class="project-link" title="演示"><i class="icon-external-link"></i></a>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">${escapeHtml(project.title)}</h3>
                        <p class="project-description">${escapeHtml(project.description.substring(0, 80))}...</p>
                        <div class="project-tags">
                            ${project.tags ? project.tags.slice(0, 3).map(tag => `<span class="tag">${escapeHtml(tag)}</span>`).join('') : ''}
                        </div>
                        <div class="project-footer">
                            <span class="project-status ${project.status}">${getProjectStatusText(project.status)}</span>
                            ${project.award ? `<span class="project-award" title="${escapeHtml(project.award)}"><i class="icon-trophy"></i></span>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 初始化统计数字动画
        function initStatsAnimation() {
            const statNumbers = document.querySelectorAll('.stat-number');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateNumber(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });

            statNumbers.forEach(number => observer.observe(number));
        }

        // 数字动画
        function animateNumber(element) {
            const target = parseInt(element.dataset.target);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 16);
        }

        // 初始化联系表单
        function initContactForm() {
            const form = document.getElementById('contact-form');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                // 这里可以添加表单提交逻辑
                showNotification('消息已发送，我们会尽快回复您！', 'success');
                form.reset();
            });
        }

        // 工具函数
        function getAnnouncementTypeText(type) {
            const types = {
                'notice': '通知',
                'event': '活动',
                'urgent': '紧急',
                'maintenance': '维护'
            };
            return types[type] || '通知';
        }

        function getProjectStatusText(status) {
            const statuses = {
                'planning': '规划中',
                'developing': '开发中',
                'completed': '已完成',
                'archived': '已归档'
            };
            return statuses[status] || '未知';
        }
    </script>
</body>
</html>

