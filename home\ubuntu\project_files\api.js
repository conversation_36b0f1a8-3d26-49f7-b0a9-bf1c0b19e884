// NIS社团网站API交互模块

class NISApi {
    constructor() {
        this.baseUrl = '';
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    // 获取CSRF令牌
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.content : '';
    }

    // 通用API请求方法
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': this.getCSRFToken()
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(this.baseUrl + url, mergedOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 带缓存的GET请求
    async get(url, useCache = true) {
        const cacheKey = `GET:${url}`;
        
        if (useCache && this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        const data = await this.request(url);
        
        if (useCache) {
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });
        }
        
        return data;
    }

    // POST请求
    async post(url, data) {
        return await this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // 获取公告列表
    async getAnnouncements() {
        return await this.get('/api/announcements');
    }

    // 获取项目列表
    async getProjects() {
        return await this.get('/api/projects');
    }

    // 获取团队成员
    async getTeam() {
        return await this.get('/api/team');
    }

    // 获取学习资源
    async getResources() {
        return await this.get('/api/resources');
    }

    // 获取活动列表
    async getEvents() {
        return await this.get('/api/events');
    }

    // 提交反馈
    async submitFeedback(feedbackData) {
        return await this.post('/api/feedback', feedbackData);
    }

    // 获取统计数据
    async getStats() {
        return await this.get('/api/stats');
    }

    // 清除缓存
    clearCache() {
        this.cache.clear();
    }
}

// 确保在页面加载时初始化API和渲染器
if (typeof window !== 'undefined') {
    window.NISApi = NISApi;
    window.DataRenderer = DataRenderer;
}

// 数据渲染工具类
class DataRenderer {
    constructor(api) {
        this.api = api;
    }

    // 渲染公告列表
    async renderAnnouncements(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        try {
            this.showLoading(container);
            const announcements = await this.api.getAnnouncements();
            
            if (announcements.length === 0) {
                container.innerHTML = this.getEmptyState('暂无最新公告');
                return;
            }

            container.innerHTML = announcements.map((ann, index) => `
                <div class="card fade-in" style="transition-delay: ${index * 100}ms;">
                    <div class="card-header">
                        <div class="flex justify-between items-start">
                            <div class="flex items-center">
                                <span class="px-3 py-1 rounded-full text-sm font-medium ${this.getPriorityClass(ann.priority)}">
                                    ${ann.type}
                                </span>
                                ${ann.priority === 'high' ? '<i class="fas fa-exclamation-triangle text-red-500 ml-2"></i>' : ''}
                            </div>
                            <span class="text-sm text-gray-500">${ann.date}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="text-xl font-bold text-gray-800 mb-3">${ann.title}</h3>
                        <p class="text-gray-600 mb-4">${ann.content}</p>
                        <div class="flex justify-between items-center text-sm text-gray-500">
                            <span>发布: ${ann.date}</span>
                            <span class="text-red-500">截止: ${ann.expire}</span>
                        </div>
                    </div>
                </div>
            `).join('');

            this.triggerAnimations(container);

        } catch (error) {
            container.innerHTML = this.getErrorState('加载公告失败');
        }
    }

    // 渲染项目列表
    async renderProjects(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        try {
            this.showLoading(container);
            const projects = await this.api.getProjects();
            
            const projectIcons = ['fa-folder-open', 'fa-shield-halved', 'fa-wifi', 'fa-code', 'fa-server', 'fa-lock'];
            
            container.innerHTML = projects.map((project, index) => `
                <div class="card fade-in" style="transition-delay: ${index * 150}ms;">
                    <div class="card-header">
                        <div class="flex justify-between items-center">
                            <i class="fas ${projectIcons[index % projectIcons.length]} text-3xl text-blue-500"></i>
                            <a href="${project.github_url}" target="_blank" class="text-gray-400 hover:text-blue-600 transition-colors">
                                <i class="fab fa-github text-2xl"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">${project.title}</h3>
                        <p class="text-gray-600 mb-4 min-h-[5rem]">${project.description}</p>
                        ${project.award ? `
                            <div class="mb-4">
                                <span class="bg-green-500 text-white py-1 px-3 rounded-full text-sm">
                                    <i class="fas fa-trophy mr-1"></i>${project.award}
                                </span>
                            </div>
                        ` : ''}
                        <div class="flex flex-wrap gap-2 mb-3">
                            ${project.tags.map(tag => `<span class="tech-tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">状态: <span class="font-medium text-blue-600">${project.status}</span></span>
                            <a href="${project.github_url}" target="_blank" class="btn btn-outline btn-sm">
                                查看详情 <i class="fas fa-external-link-alt ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            `).join('');

            this.triggerAnimations(container);

        } catch (error) {
            container.innerHTML = this.getErrorState('加载项目失败');
        }
    }

    // 渲染团队成员
    async renderTeam(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        try {
            this.showLoading(container);
            const team = await this.api.getTeam();
            
            container.innerHTML = team.map((member, index) => `
                <div class="card text-center fade-in" style="transition-delay: ${index * 150}ms;">
                    <div class="card-body">
                        <div class="w-24 h-24 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-user text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">${member.name}</h3>
                        <p class="text-blue-600 font-medium mb-2">${member.role}</p>
                        <p class="text-sm text-gray-600 mb-4">${member.achievement}</p>
                        <div class="flex flex-wrap justify-center gap-2">
                            ${member.skills.map(skill => `
                                <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs">${skill}</span>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `).join('');

            this.triggerAnimations(container);

        } catch (error) {
            container.innerHTML = this.getErrorState('加载团队信息失败');
        }
    }

    // 渲染学习资源
    async renderResources(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        try {
            this.showLoading(container);
            const resources = await this.api.getResources();

            // 按分类分组资源
            const groupedResources = {};
            resources.forEach(resource => {
                const category = resource.category || '其他';
                if (!groupedResources[category]) {
                    groupedResources[category] = [];
                }
                groupedResources[category].push(resource);
            });

            const categories = Object.keys(groupedResources);

            container.innerHTML = categories.map((category, categoryIndex) => `
                <div class="mb-8 fade-in" style="transition-delay: ${categoryIndex * 200}ms;">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">${category}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        ${groupedResources[category].map((resource, index) => `
                            <div class="card fade-in" style="transition-delay: ${(categoryIndex * 200) + (index * 100)}ms;">
                                <div class="card-body">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-gray-800">${resource.title}</h4>
                                        <span class="text-xs px-2 py-1 rounded-full ${this.getResourceTypeClass(resource.type)}">${resource.type}</span>
                                    </div>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">${resource.description}</p>
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700">${resource.difficulty}</span>
                                        <span class="text-xs text-gray-500">
                                            <i class="fas fa-download mr-1"></i>${resource.download_count || 0}
                                        </span>
                                    </div>
                                    <div class="flex flex-wrap gap-1 mb-3">
                                        ${(resource.tags || []).slice(0, 3).map(tag => `
                                            <span class="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded">${tag}</span>
                                        `).join('')}
                                    </div>
                                    <a href="${resource.url}" target="_blank" class="btn btn-primary btn-sm w-full">
                                        <i class="fas fa-external-link-alt mr-1"></i>查看资源
                                    </a>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');

            this.triggerAnimations(container);

        } catch (error) {
            console.error('加载学习资源失败:', error);
            container.innerHTML = this.getErrorState('加载学习资源失败');
        }
    }

    // 工具方法
    showLoading(container) {
        container.innerHTML = `
            <div class="flex justify-center items-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <span class="ml-4 text-gray-600">加载中...</span>
            </div>
        `;
    }

    getEmptyState(message) {
        return `
            <div class="text-center py-12">
                <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">${message}</p>
            </div>
        `;
    }

    getErrorState(message) {
        return `
            <div class="text-center py-12">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <p class="text-gray-600">${message}</p>
                <button onclick="location.reload()" class="btn btn-primary mt-4">重新加载</button>
            </div>
        `;
    }

    getPriorityClass(priority) {
        const classes = {
            high: 'bg-red-100 text-red-800',
            medium: 'bg-yellow-100 text-yellow-800',
            low: 'bg-blue-100 text-blue-800'
        };
        return classes[priority] || classes.low;
    }

    getResourceTypeClass(type) {
        const classes = {
            'video': 'bg-red-100 text-red-700',
            'file': 'bg-blue-100 text-blue-700',
            'link': 'bg-green-100 text-green-700',
            'document': 'bg-purple-100 text-purple-700'
        };
        return classes[type] || 'bg-gray-100 text-gray-700';
    }

    triggerAnimations(container) {
        setTimeout(() => {
            container.querySelectorAll('.fade-in').forEach(el => {
                el.classList.add('visible');
            });
        }, 100);
    }
}

// 全局实例
window.nisApi = new NISApi();
window.dataRenderer = new DataRenderer(window.nisApi);
