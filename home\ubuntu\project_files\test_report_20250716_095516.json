{
  "timestamp": "2025-07-16T09:55:16.012709",
  "summary": {
    "total": 18,
    "passed": 16,
    "failed": 2,
    "success_rate": 88.88888888888889
  },
  "results": [
    {
      "endpoint": "/health",
      "method": "GET",
      "status_code": 200,
      "expected_status": 200,
      "success": true,
      "description": "健康检查",
      "response_time": 0.002863,
      "response_data": {
        "database": "healthy",
        "status": "healthy",
        "timestamp": "2025-07-16T09:55:15.964093"
      }
    },
    {
      "endpoint": "/api/csrf-token",
      "method": "GET",
      "status_code": 200,
      "expected_status": 200,
      "success": true,
      "description": "获取CSRF令牌",
      "response_time": 0.002238,
      "response_data": {
        "csrf_token": "b44c8c4017284e2a27ea8c2caa3abdc046281b661541654488c0def589d9a554"
      }
    },
    {
      "endpoint": "/api/announcements",
      "method": "GET",
      "status_code": 200,
      "expected_status": 200,
      "success": true,
      "description": "获取公告列表",
      "response_time": 0.003053,
      "response_data": {
        "data": [
          {
            "content": "这是一个改进的安全新闻链接管理系统，具有增强的安全性和功能。",
            "created_at": "Wed, 16 Jul 2025 13:44:01 GMT",
            "id": 3,
            "is_pinned": 1,