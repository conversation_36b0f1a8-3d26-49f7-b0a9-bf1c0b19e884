<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习资源 - NIS网络信息安全社团</title>
    <meta name="description" content="网络安全学习资源、工具教程、技术文档和实战指南">
    <meta name="keywords" content="网络安全,学习资源,工具教程,技术文档,实战指南">
    
    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style_improved.css">
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white/90 backdrop-blur-sm shadow-sm sticky top-0 z-50 transition-all duration-300">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shield-alt text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-800">NIS安全社团</h1>
                    <p class="text-xs text-gray-600">网络信息安全社团</p>
                </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-600 hover:text-blue-600 transition-colors">首页</a>
                <a href="security_news.html" class="text-gray-600 hover:text-blue-600 transition-colors">安全资讯</a>
                <a href="projects.html" class="text-gray-600 hover:text-blue-600 transition-colors">项目展示</a>
                <a href="resources.html" class="text-blue-600 font-semibold">学习资源</a>
                <a href="events.html" class="text-gray-600 hover:text-blue-600 transition-colors">活动回顾</a>
                <a href="about.html" class="text-gray-600 hover:text-blue-600 transition-colors">关于我们</a>
            </div>
            
            <div class="md:hidden">
                <button class="text-gray-600 hover:text-blue-600">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-green-600 to-blue-600 text-white py-20">
        <div class="container mx-auto px-6 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                学习资源库
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90">
                精选网络安全学习资源，助力技能提升
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-book mr-2"></i>
                    入门教程
                </div>
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-tools mr-2"></i>
                    工具指南
                </div>
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                    <i class="fas fa-video mr-2"></i>
                    视频教程
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-8 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div class="flex flex-wrap gap-2">
                    <button class="filter-btn active" data-category="all">全部</button>
                    <button class="filter-btn" data-category="入门教程">入门教程</button>
                    <button class="filter-btn" data-category="工具教程">工具教程</button>
                    <button class="filter-btn" data-category="理论基础">理论基础</button>
                    <button class="filter-btn" data-category="实战案例">实战案例</button>
                </div>
                <div class="flex gap-2">
                    <select id="difficulty-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="all">所有难度</option>
                        <option value="beginner">初级</option>
                        <option value="intermediate">中级</option>
                        <option value="advanced">高级</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources Grid -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div id="resources-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 资源卡片将通过JavaScript动态加载 -->
            </div>
            
            <!-- Loading -->
            <div id="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-2 text-gray-600">加载中...</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">NIS安全社团</h3>
                            <p class="text-gray-400">网络信息安全社团</p>
                        </div>
                    </div>
                    <p class="text-gray-400 mb-6">
                        致力于网络安全技术研究与实践，培养优秀的网络安全人才，为网络空间安全贡献力量。
                    </p>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">快速链接</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">首页</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">关于我们</a></li>
                        <li><a href="projects.html" class="text-gray-400 hover:text-white transition-colors">项目展示</a></li>
                        <li><a href="security_news.html" class="text-gray-400 hover:text-white transition-colors">安全资讯</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li class="text-gray-400">
                            <i class="fas fa-envelope mr-2"></i>
                            <EMAIL>
                        </li>
                        <li class="text-gray-400">
                            <i class="fab fa-qq mr-2"></i>
                            QQ群: 123456789
                        </li>
                        <li class="text-gray-400">
                            <i class="fab fa-github mr-2"></i>
                            GitHub: NIS-Security
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 NIS网络信息安全社团. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/common_improved.js"></script>
    <script src="/static/js/api.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadResources();
            initFilters();
        });

        // 加载学习资源
        function loadResources() {
            const container = document.getElementById('resources-container');
            const loading = document.getElementById('loading');
            
            fetch('/api/resources')
                .then(response => response.json())
                .then(data => {
                    loading.style.display = 'none';
                    if (data.success && data.data) {
                        renderResourceCards(data.data);
                    } else {
                        container.innerHTML = '<div class="col-span-full text-center text-gray-500">暂无学习资源</div>';
                    }
                })
                .catch(error => {
                    console.error('加载资源失败:', error);
                    loading.style.display = 'none';
                    container.innerHTML = '<div class="col-span-full text-center text-red-500">加载失败，请稍后重试</div>';
                });
        }

        // 渲染资源卡片
        function renderResourceCards(resources) {
            const container = document.getElementById('resources-container');
            container.innerHTML = resources.map(resource => `
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            ${resource.category || '学习资源'}
                        </span>
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            ${getDifficultyText(resource.difficulty)}
                        </span>
                    </div>
                    <h3 class="text-lg font-semibold mb-3">${resource.title}</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">${resource.description || '暂无描述'}</p>
                    <div class="flex items-center justify-between">
                        <a href="${resource.url}" target="_blank" 
                           class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                            ${getResourceTypeIcon(resource.type)} 查看资源
                        </a>
                        <span class="text-gray-400 text-sm">
                            <i class="fas fa-download mr-1"></i>${resource.download_count || 0}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // 获取难度文本
        function getDifficultyText(difficulty) {
            const difficulties = {
                'beginner': '初级',
                'intermediate': '中级',
                'advanced': '高级'
            };
            return difficulties[difficulty] || '未知';
        }

        // 获取资源类型图标
        function getResourceTypeIcon(type) {
            const icons = {
                'link': '<i class="fas fa-external-link-alt mr-1"></i>',
                'file': '<i class="fas fa-file-download mr-1"></i>',
                'video': '<i class="fas fa-play mr-1"></i>',
                'document': '<i class="fas fa-file-alt mr-1"></i>'
            };
            return icons[type] || '<i class="fas fa-link mr-1"></i>';
        }

        // 初始化过滤器
        function initFilters() {
            // 分类过滤器
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    // 这里可以添加过滤逻辑
                });
            });
        }
    </script>

    <style>
        .filter-btn {
            @apply px-4 py-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-100 transition-colors;
        }
        .filter-btn.active {
            @apply bg-blue-600 text-white border-blue-600;
        }
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</body>
</html>
