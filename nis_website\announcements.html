<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="csrf-token-placeholder">
    <meta name="description" content="NIS社团公告中心，发布最新通知、活动安排、招新信息等重要公告">
    <meta name="keywords" content="NIS公告,社团通知,招新信息,活动安排,最新消息">
    <title>公告中心 - NIS网络信息安全社团</title>
    
    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style_improved.css">
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white/90 backdrop-blur-sm shadow-sm sticky top-0 z-50 transition-all duration-300">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <div class="h-10 w-10 mr-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <i class="fas fa-shield-alt text-white"></i>
                </div>
                <span class="font-bold text-xl text-slate-800">NIS社团</span>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-6 text-slate-600">
                <a href="index.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-home mr-1"></i>首页
                </a>
                <a href="about.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-users mr-1"></i>关于我们
                </a>
                <a href="projects.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-code mr-1"></i>项目展示
                </a>
                <a href="resources.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-book mr-1"></i>学习资源
                </a>
                <a href="events.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-calendar mr-1"></i>活动回顾
                </a>
                <a href="security_news.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-newspaper mr-1"></i>安全资讯
                </a>
                <a href="announcements.html" class="nav-link text-blue-600 font-semibold">
                    <i class="fas fa-bullhorn mr-1"></i>公告中心
                </a>
                <a href="join.html" class="nav-link hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-user-plus mr-1"></i>加入我们
                </a>
            </div>
            
            <div class="md:hidden">
                <button id="menu-btn" class="text-slate-600 focus:outline-none">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </div>
        </nav>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden px-6 pb-4 bg-white border-t border-gray-200">
            <div class="space-y-2 py-4">
                <a href="index.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="about.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-users mr-2"></i>关于我们
                </a>
                <a href="projects.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-code mr-2"></i>项目展示
                </a>
                <a href="resources.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-book mr-2"></i>学习资源
                </a>
                <a href="events.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>活动回顾
                </a>
                <a href="security_news.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-newspaper mr-2"></i>安全热点
                </a>
                <a href="announcements.html" class="block py-2 text-blue-600 font-semibold">
                    <i class="fas fa-bullhorn mr-2"></i>公告中心
                </a>
                <a href="join.html" class="block py-2 text-slate-600 hover:text-blue-600 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>加入我们
                </a>
            </div>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="gradient-bg text-white section-padding">
            <div class="container mx-auto px-6 text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6 fade-in">📢 公告中心</h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto fade-in">
                    获取最新的社团通知、活动安排和重要信息
                </p>
            </div>
        </section>

        <!-- Announcements List -->
        <section class="section-padding bg-gray-50">
            <div class="container mx-auto px-6">
                <div id="announcements-container" class="max-w-4xl mx-auto">
                    <!-- 动态加载公告内容 -->
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-blue-500"></i>
                        <p class="mt-4 text-slate-600">正在加载公告...</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Info -->
        <section class="section-padding bg-white">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="section-title">快速信息</h2>
                    <p class="section-subtitle">
                        重要信息一目了然
                    </p>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="card text-center fade-in">
                        <div class="card-body">
                            <i class="fas fa-users text-3xl text-blue-600 mb-4"></i>
                            <h3 class="text-lg font-bold mb-2">QQ群</h3>
                            <p class="text-gray-600 mb-3">242050951</p>
                            <button onclick="copyToClipboard('242050951')" class="btn btn-outline btn-sm">
                                <i class="fas fa-copy mr-1"></i>复制
                            </button>
                        </div>
                    </div>
                    
                    <div class="card text-center fade-in" style="transition-delay: 150ms;">
                        <div class="card-body">
                            <i class="fab fa-tiktok text-3xl text-red-600 mb-4"></i>
                            <h3 class="text-lg font-bold mb-2">抖音号</h3>
                            <p class="text-gray-600 mb-3">21647629167</p>
                            <button onclick="copyToClipboard('21647629167')" class="btn btn-outline btn-sm">
                                <i class="fas fa-copy mr-1"></i>复制
                            </button>
                        </div>
                    </div>
                    
                    <div class="card text-center fade-in" style="transition-delay: 300ms;">
                        <div class="card-body">
                            <i class="fas fa-clock text-3xl text-green-600 mb-4"></i>
                            <h3 class="text-lg font-bold mb-2">活动时间</h3>
                            <p class="text-gray-600 mb-3">每周三、五<br>19:00-21:00</p>
                            <span class="text-sm text-green-600 font-medium">定期举行</span>
                        </div>
                    </div>
                    
                    <div class="card text-center fade-in" style="transition-delay: 450ms;">
                        <div class="card-body">
                            <i class="fas fa-map-marker-alt text-3xl text-purple-600 mb-4"></i>
                            <h3 class="text-lg font-bold mb-2">活动地点</h3>
                            <p class="text-gray-600 mb-3">成都工业职业技术学院<br>金堂校区</p>
                            <span class="text-sm text-purple-600 font-medium">具体教室见群通知</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="section-padding bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-subtitle">
                        有问题？随时联系我们
                    </p>
                </div>
                
                <div class="max-w-2xl mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <form id="contact-form" class="space-y-6">
                                <div>
                                    <label for="contact-name" class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                                    <input type="text" id="contact-name" name="name" required 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="请输入您的姓名">
                                </div>
                                <div>
                                    <label for="contact-email" class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                    <input type="email" id="contact-email" name="email" required 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="请输入您的邮箱">
                                </div>
                                <div>
                                    <label for="contact-subject" class="block text-sm font-medium text-gray-700 mb-2">主题</label>
                                    <select id="contact-subject" name="subject" required 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">请选择主题</option>
                                        <option value="join">加入社团</option>
                                        <option value="activity">活动咨询</option>
                                        <option value="cooperation">合作洽谈</option>
                                        <option value="feedback">意见反馈</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="contact-message" class="block text-sm font-medium text-gray-700 mb-2">留言内容</label>
                                    <textarea id="contact-message" name="message" rows="5" required 
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                              placeholder="请详细描述您的问题或建议..."></textarea>
                                </div>
                                <button type="submit" 
                                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition duration-300 shadow-lg shadow-blue-500/30">
                                    <i class="fas fa-paper-plane mr-2"></i>发送消息
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="section-padding gradient-bg text-white">
            <div class="container mx-auto px-6 text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6 fade-in">
                    准备好加入我们了吗？
                </h2>
                <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto fade-in">
                    关注我们的公告，不错过任何精彩活动
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
                    <a href="join.html" class="btn btn-secondary">
                        立即加入 <i class="fas fa-rocket ml-2"></i>
                    </a>
                    <a href="about.html" class="btn btn-outline">
                        了解更多 <i class="fas fa-info-circle ml-2"></i>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white">
        <div class="container mx-auto px-6 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="h-10 w-10 mr-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white"></i>
                        </div>
                        <span class="font-bold text-xl">NIS网络信息安全社团</span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        成都工业职业技术学院网络信息安全社团，致力于培养网络安全人才，推动校园网络安全文化建设。
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-qq text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-weixin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-300 hover:text-white transition-colors">首页</a></li>
                        <li><a href="about.html" class="text-gray-300 hover:text-white transition-colors">关于我们</a></li>
                        <li><a href="projects.html" class="text-gray-300 hover:text-white transition-colors">项目展示</a></li>
                        <li><a href="resources.html" class="text-gray-300 hover:text-white transition-colors">学习资源</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">联系我们</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><i class="fas fa-map-marker-alt mr-2"></i>成都工业职业技术学院</li>
                        <li><i class="fas fa-users mr-2"></i>QQ群: 242050951</li>
                        <li><i class="fab fa-tiktok mr-2"></i>抖音: 21647629167</li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 NIS网络信息安全社团. 保留所有权利.</p>
                <p class="mt-2 text-sm">Designed & Built with ❤️ by NIS Club</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/common_improved.js"></script>
    <script src="/static/js/api.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载公告
            window.dataRenderer.renderAnnouncements('announcements-container');
            
            // 绑定联系表单事件
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', handleContactSubmit);
            }
        });

        async function handleContactSubmit(event) {
            event.preventDefault();
            
            const form = event.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // 显示提交状态
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>发送中...';
            submitBtn.disabled = true;
            
            try {
                const formData = new FormData(form);
                const data = {
                    name: formData.get('name'),
                    email: formData.get('email'),
                    subject: formData.get('subject'),
                    message: formData.get('message')
                };
                
                // 这里可以调用API发送消息
                // await window.nisApi.post('/api/contact', data);
                
                // 模拟发送成功
                setTimeout(() => {
                    window.NISWebsite.prototype.showNotification('消息发送成功，我们会尽快回复您！', 'success');
                    form.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 1000);
                
            } catch (error) {
                window.NISWebsite.prototype.showNotification('发送失败，请稍后重试', 'error');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
