#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版数据库设置 - 用于URL管理系统
"""

import mysql.connector
from mysql.connector import Error
import json

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS nis_security_news CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ 数据库创建成功")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"❌ 创建数据库失败: {e}")
        return False
    
    return True

def create_tables():
    """创建简化的表结构"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='root',
            database='nis_security_news',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 1. 新闻分类表
        categories_table = """
        CREATE TABLE IF NOT EXISTS news_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
            description TEXT COMMENT '分类描述',
            sort_order INT DEFAULT 0 COMMENT '排序',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻分类表'
        """
        
        # 2. 新闻链接表
        news_links_table = """
        CREATE TABLE IF NOT EXISTS news_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(500) NOT NULL COMMENT '文章标题',
            url VARCHAR(1000) NOT NULL COMMENT '文章链接',
            description TEXT COMMENT '文章描述',
            category_id INT COMMENT '分类ID',
            status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
            views INT DEFAULT 0 COMMENT '浏览次数',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES news_categories(id) ON DELETE SET NULL,
            INDEX idx_status (status),
            INDEX idx_category (category_id),
            INDEX idx_created_at (created_at),
            INDEX idx_featured (is_featured),
            UNIQUE INDEX idx_url_unique (url(255)),
            FULLTEXT INDEX idx_search (title, description)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻链接表'
        """

        # 3. 项目展示表
        projects_table = """
        CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '项目标题',
            description TEXT COMMENT '项目描述',
            image_url VARCHAR(500) COMMENT '项目图片',
            github_url VARCHAR(500) COMMENT 'GitHub链接',
            demo_url VARCHAR(500) COMMENT '演示链接',
            technologies JSON COMMENT '使用技术',
            status ENUM('planning', 'developing', 'completed', 'archived') DEFAULT 'developing' COMMENT '项目状态',
            award VARCHAR(200) COMMENT '获奖情况',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            sort_order INT DEFAULT 0 COMMENT '排序',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_featured (is_featured),
            INDEX idx_sort (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目展示表'
        """

        # 4. 学习资源表
        resources_table = """
        CREATE TABLE IF NOT EXISTS learning_resources (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '资源标题',
            description TEXT COMMENT '资源描述',
            url VARCHAR(500) COMMENT '资源链接',
            file_path VARCHAR(500) COMMENT '文件路径',
            resource_type ENUM('link', 'file', 'video', 'document') DEFAULT 'link' COMMENT '资源类型',
            category VARCHAR(100) COMMENT '资源分类',
            difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
            tags JSON COMMENT '标签',
            download_count INT DEFAULT 0 COMMENT '下载次数',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_type (resource_type),
            INDEX idx_category (category),
            INDEX idx_difficulty (difficulty),
            INDEX idx_status (status),
            INDEX idx_featured (is_featured)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习资源表'
        """

        # 5. 活动回顾表
        events_table = """
        CREATE TABLE IF NOT EXISTS events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '活动标题',
            description TEXT COMMENT '活动描述',
            content LONGTEXT COMMENT '活动详情',
            event_date DATE COMMENT '活动日期',
            location VARCHAR(200) COMMENT '活动地点',
            image_url VARCHAR(500) COMMENT '活动图片',
            gallery JSON COMMENT '图片集',
            participants_count INT DEFAULT 0 COMMENT '参与人数',
            status ENUM('upcoming', 'ongoing', 'completed', 'cancelled') DEFAULT 'upcoming' COMMENT '活动状态',
            is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_date (event_date),
            INDEX idx_status (status),
            INDEX idx_featured (is_featured)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动回顾表'
        """

        # 6. 团队成员表
        members_table = """
        CREATE TABLE IF NOT EXISTS team_members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT '姓名',
            position VARCHAR(100) COMMENT '职位',
            bio TEXT COMMENT '个人简介',
            avatar_url VARCHAR(500) COMMENT '头像链接',
            skills JSON COMMENT '技能标签',
            github_url VARCHAR(200) COMMENT 'GitHub链接',
            email VARCHAR(200) COMMENT '邮箱',
            join_date DATE COMMENT '加入日期',
            is_core BOOLEAN DEFAULT FALSE COMMENT '是否核心成员',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否在职',
            sort_order INT DEFAULT 0 COMMENT '排序',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_core (is_core),
            INDEX idx_active (is_active),
            INDEX idx_sort (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队成员表'
        """
        
        # 7. 系统配置表
        system_config_table = """
        CREATE TABLE IF NOT EXISTS system_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
            config_value TEXT COMMENT '配置值',
            description TEXT COMMENT '配置描述',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'
        """

        # 8. 公告表
        announcements_table = """
        CREATE TABLE IF NOT EXISTS announcements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL COMMENT '公告标题',
            content TEXT NOT NULL COMMENT '公告内容',
            type ENUM('notice', 'event', 'urgent') DEFAULT 'notice' COMMENT '公告类型',
            status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
            is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
            publish_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_type (type),
            INDEX idx_pinned (is_pinned),
            INDEX idx_publish_time (publish_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表'
        """
        
        # 执行创建表的SQL
        tables = [
            ("新闻分类表", categories_table),
            ("新闻链接表", news_links_table),
            ("项目展示表", projects_table),
            ("学习资源表", resources_table),
            ("活动回顾表", events_table),
            ("团队成员表", members_table),
            ("系统配置表", system_config_table),
            ("公告表", announcements_table)
        ]
        
        for table_name, table_sql in tables:
            cursor.execute(table_sql)
            print(f"✅ {table_name}创建成功")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"❌ 创建表失败: {e}")
        return False
    
    return True

def insert_default_data():
    """插入默认数据"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='root',
            database='nis_security_news',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 插入默认分类
        categories = [
            ('网络安全', '网络安全相关新闻', 1),
            ('漏洞情报', '安全漏洞和威胁情报', 2),
            ('技术分析', '安全技术分析文章', 3),
            ('行业动态', '安全行业动态新闻', 4),
            ('工具资源', '安全工具和资源', 5)
        ]
        
        for name, desc, sort_order in categories:
            cursor.execute("""
                INSERT IGNORE INTO news_categories (name, description, sort_order) 
                VALUES (%s, %s, %s)
            """, (name, desc, sort_order))
        
        # 插入示例新闻链接
        sample_links = [
            ('FreeBuf安全资讯', 'https://www.freebuf.com/', '国内知名网络安全媒体平台', 1, True),
            ('安全客', 'https://www.anquanke.com/', '专业的网络安全技术媒体', 1, True),
            ('Seebug漏洞平台', 'https://www.seebug.org/', '专业的漏洞情报平台', 2, True),
            ('CNVD国家信息安全漏洞库', 'https://www.cnvd.org.cn/', '国家级漏洞信息发布平台', 2, False),
            ('Kali Linux官网', 'https://www.kali.org/', '渗透测试专用Linux发行版', 3, False)
        ]

        for title, url, desc, category_id, is_featured in sample_links:
            cursor.execute("""
                INSERT IGNORE INTO news_links (title, url, description, category_id, is_featured)
                VALUES (%s, %s, %s, %s, %s)
            """, (title, url, desc, category_id, is_featured))

        # 插入示例项目
        sample_projects = [
            ('校园网络监控平台', '基于Python和ELK Stack的校园网络流量监控与异常检测平台',
             '/static/images/project1.jpg', 'https://github.com/nis/network-monitor',
             'http://demo.nis.edu.cn/monitor', '["Python", "Elasticsearch", "Flask", "Docker"]',
             'completed', '获2023年四川省大学生网络安全竞赛二等奖', True),
            ('Web安全扫描器', '自主开发的Web应用安全漏洞扫描工具',
             '/static/images/project2.jpg', 'https://github.com/nis/web-scanner',
             '', '["Python", "Requests", "BeautifulSoup", "SQLite"]',
             'completed', '获校级创新创业大赛一等奖', True),
            ('密码学实验平台', '用于密码学教学的在线实验平台',
             '/static/images/project3.jpg', 'https://github.com/nis/crypto-lab',
             'http://crypto.nis.edu.cn', '["Vue.js", "Node.js", "MySQL", "WebCrypto"]',
             'developing', '', False)
        ]

        for title, desc, img, github, demo, tech, status, award, featured in sample_projects:
            cursor.execute("""
                INSERT IGNORE INTO projects (title, description, image_url, github_url, demo_url,
                                           technologies, status, award, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, desc, img, github, demo, tech, status, award, featured))

        # 插入示例学习资源
        sample_resources = [
            ('网络安全入门指南', '适合初学者的网络安全学习路线图',
             'https://github.com/nis/security-guide', '', 'link', '入门教程', 'beginner',
             '["网络安全", "入门", "学习路线"]', True),
            ('渗透测试工具集', '常用渗透测试工具的使用教程合集',
             '', '/static/files/pentest-tools.pdf', 'file', '工具教程', 'intermediate',
             '["渗透测试", "工具", "教程"]', True),
            ('密码学基础视频', '密码学基础知识讲解视频',
             'https://www.bilibili.com/video/BV1xx411c7mu', '', 'video', '理论基础', 'beginner',
             '["密码学", "视频", "基础"]', False)
        ]

        for title, desc, url, file_path, res_type, category, difficulty, tags, featured in sample_resources:
            cursor.execute("""
                INSERT IGNORE INTO learning_resources (title, description, url, file_path,
                                                     resource_type, category, difficulty, tags, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, desc, url, file_path, res_type, category, difficulty, tags, featured))

        # 插入示例活动
        sample_events = [
            ('2024年网络安全技能大赛', '社团年度网络安全技能竞赛活动',
             '本次大赛包含Web安全、逆向工程、密码学等多个方向的挑战题目，旨在提升成员的实战技能。',
             '2024-03-15', '学校机房A301', '/static/images/event1.jpg',
             '["event1_1.jpg", "event1_2.jpg", "event1_3.jpg"]', 45, 'completed', True),
            ('企业安全专家讲座', '邀请知名企业安全专家分享实战经验',
             '本次讲座邀请了腾讯安全团队的资深专家，分享企业级安全防护的最佳实践。',
             '2024-04-20', '学术报告厅', '/static/images/event2.jpg',
             '["event2_1.jpg", "event2_2.jpg"]', 120, 'completed', True)
        ]

        for title, desc, content, date, location, img, gallery, participants, status, featured in sample_events:
            cursor.execute("""
                INSERT IGNORE INTO events (title, description, content, event_date, location,
                                         image_url, gallery, participants_count, status, is_featured)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, desc, content, date, location, img, gallery, participants, status, featured))

        # 插入示例团队成员
        sample_members = [
            ('张三', '社团主席', '计算机科学与技术专业，专注于Web安全和渗透测试',
             '/static/images/member1.jpg', '["Python", "Web安全", "渗透测试", "Linux"]',
             'https://github.com/zhangsan', '<EMAIL>', '2022-09-01', True, True, 1),
            ('李四', '技术副主席', '网络工程专业，擅长网络安全和系统管理',
             '/static/images/member2.jpg', '["网络安全", "系统管理", "防火墙", "IDS"]',
             'https://github.com/lisi', '<EMAIL>', '2022-09-01', True, True, 2),
            ('王五', '项目经理', '信息安全专业，负责社团项目开发和管理',
             '/static/images/member3.jpg', '["项目管理", "Java", "数据库", "DevOps"]',
             'https://github.com/wangwu', '<EMAIL>', '2023-03-01', True, True, 3)
        ]

        for name, position, bio, avatar, skills, github, email, join_date, is_core, is_active, sort_order in sample_members:
            cursor.execute("""
                INSERT IGNORE INTO team_members (name, position, bio, avatar_url, skills,
                                               github_url, email, join_date, is_core, is_active, sort_order)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (name, position, bio, avatar, skills, github, email, join_date, is_core, is_active, sort_order))
        
        # 插入系统配置
        configs = [
            ('site_name', 'NIS社团安全新闻', '网站名称'),
            ('admin_email', '<EMAIL>', '管理员邮箱'),
            ('max_links_per_page', '20', '每页显示链接数'),
            ('enable_featured', 'true', '是否启用推荐功能')
        ]
        
        for key, value, desc in configs:
            cursor.execute("""
                INSERT IGNORE INTO system_config (config_key, config_value, description) 
                VALUES (%s, %s, %s)
            """, (key, value, desc))
        
        # 插入示例公告
        cursor.execute("""
            INSERT IGNORE INTO announcements (title, content, type, is_pinned) 
            VALUES (%s, %s, %s, %s)
        """, ('欢迎使用NIS社团安全新闻系统', '这是一个简化的安全新闻链接管理系统，管理员可以在后台添加新闻链接。', 'notice', True))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ 默认数据插入成功")
        
    except Error as e:
        print(f"❌ 插入默认数据失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始初始化简化版数据库...")
    print("=" * 50)
    
    # 1. 创建数据库
    if not create_database():
        print("❌ 数据库创建失败，停止初始化")
        return
    
    # 2. 创建表
    if not create_tables():
        print("❌ 表创建失败，停止初始化")
        return
    
    # 3. 插入默认数据
    if not insert_default_data():
        print("❌ 默认数据插入失败")
        return
    
    print("=" * 50)
    print("🎉 简化版数据库初始化完成！")
    print("\n📊 数据库结构:")
    print("  - news_categories: 新闻分类")
    print("  - news_links: 新闻链接")
    print("  - system_config: 系统配置")
    print("  - announcements: 公告")
    print("\n🔗 主要功能:")
    print("  - 管理员可在后台添加新闻链接")
    print("  - 前端显示链接预览和跳转")
    print("  - 支持分类管理和搜索")

if __name__ == "__main__":
    main()
