/* NIS安全社团网站 - 改进版样式 */

/* 基础重置和变量 */
:root {
    /* 主色调 */
    --primary-color: #0066cc;
    --primary-dark: #004499;
    --primary-light: #3388dd;
    --secondary-color: #ff6b35;
    --accent-color: #00d4aa;
    
    /* 中性色 */
    --dark-bg: #0a0e1a;
    --dark-surface: #1a1f2e;
    --dark-card: #242938;
    --dark-border: #2d3748;
    --light-text: #ffffff;
    --gray-text: #a0aec0;
    --muted-text: #718096;
    
    /* 状态色 */
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    --info-color: #4299e1;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* 字体 */
    --font-family: 'Inter', 'Segoe UI', 'Microsoft YaHei', sans-serif;
    --font-mono: 'Fira Code', 'Consolas', monospace;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(0, 102, 204, 0.3);
    
    /* 边框半径 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 过渡 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--light-text);
    background: var(--dark-bg);
    overflow-x: hidden;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(10, 14, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--dark-border);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(10, 14, 26, 0.98);
    box-shadow: var(--shadow-lg);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--light-text);
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    align-items: center;
}

.nav-link {
    color: var(--gray-text);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--light-text);
    background: rgba(255, 255, 255, 0.1);
}

.nav-link.join-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--light-text);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
}

.nav-link.join-btn:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-glow);
}

/* 汉堡菜单 */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background: var(--light-text);
    border-radius: 2px;
    transition: var(--transition-fast);
}

/* 英雄区域 */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(10, 14, 26, 0.8), rgba(26, 31, 46, 0.6));
    z-index: -1;
}

.hero-content {
    text-align: center;
    z-index: 1;
    max-width: 800px;
    padding: var(--spacing-xl);
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    animation: fadeInUp 1s ease 0.5s forwards;
}

.title-line {
    display: block;
    background: linear-gradient(135deg, var(--light-text), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-text);
    margin-bottom: var(--spacing-2xl);
    opacity: 0;
    animation: fadeInUp 1s ease 0.7s forwards;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0;
    animation: fadeInUp 1s ease 0.9s forwards;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    animation: fadeIn 1s ease 1.5s forwards;
}

.scroll-arrow {
    width: 24px;
    height: 24px;
    border: 2px solid var(--gray-text);
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: bounce 2s infinite;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--light-text);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary {
    background: transparent;
    color: var(--light-text);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 区域样式 */
section {
    padding: var(--spacing-3xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--light-text), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--gray-text);
    max-width: 600px;
    margin: 0 auto;
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-fast);
}

.view-all-link:hover {
    color: var(--primary-light);
    transform: translateX(4px);
}

/* 特色功能 */
.features {
    background: var(--dark-surface);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--dark-card);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    border: 1px solid var(--dark-border);
    text-align: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--light-text);
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--light-text);
}

.feature-description {
    color: var(--gray-text);
    line-height: 1.7;
}

/* 公告区域 */
.announcements-container {
    max-width: 800px;
    margin: 0 auto;
}

.announcements-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.announcement-card {
    background: var(--dark-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--dark-border);
    transition: var(--transition-normal);
}

.announcement-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.announcement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.announcement-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
}

.announcement-type.notice {
    background: rgba(66, 153, 225, 0.2);
    color: var(--info-color);
}

.announcement-type.event {
    background: rgba(72, 187, 120, 0.2);
    color: var(--success-color);
}

.announcement-type.urgent {
    background: rgba(245, 101, 101, 0.2);
    color: var(--error-color);
}

.announcement-date {
    color: var(--muted-text);
    font-size: 0.875rem;
}

.announcement-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--light-text);
}

.announcement-content {
    color: var(--gray-text);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.read-more:hover {
    color: var(--primary-light);
}

/* 项目预览 */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.project-card {
    background: var(--dark-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--dark-border);
    overflow: hidden;
    transition: var(--transition-normal);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-links {
    display: flex;
    gap: var(--spacing-md);
}

.project-link {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--light-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition-fast);
}

.project-link:hover {
    background: var(--primary-light);
    transform: scale(1.1);
}

.project-content {
    padding: var(--spacing-xl);
}

.project-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--light-text);
}

.project-description {
    color: var(--gray-text);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 102, 204, 0.2);
    color: var(--primary-light);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.project-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.project-status.completed {
    background: rgba(72, 187, 120, 0.2);
    color: var(--success-color);
}

.project-status.developing {
    background: rgba(237, 137, 54, 0.2);
    color: var(--warning-color);
}

.project-award {
    color: var(--warning-color);
    font-size: 1.125rem;
}

/* 统计数据 */
.stats {
    background: var(--dark-surface);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-xl);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.stat-label {
    font-size: 1.125rem;
    color: var(--gray-text);
    font-weight: 500;
}

/* 联系我们 */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.contact-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--light-text);
}

.contact-description {
    color: var(--gray-text);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.contact-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--gray-text);
}

.contact-method i {
    width: 20px;
    color: var(--primary-color);
}

.contact-form {
    background: var(--dark-card);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--dark-border);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: var(--radius-md);
    color: var(--light-text);
    font-family: inherit;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--muted-text);
}

/* 页脚 */
.footer {
    background: var(--dark-surface);
    border-top: 1px solid var(--dark-border);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.footer-description {
    color: var(--gray-text);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--dark-card);
    color: var(--gray-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--light-text);
    transform: translateY(-2px);
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--light-text);
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.footer-links a {
    color: var(--gray-text);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--dark-border);
    color: var(--muted-text);
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--light-text);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: var(--z-fixed);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 加载和状态 */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    color: var(--gray-text);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--dark-border);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.empty-state,
.error-state {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--gray-text);
}

.error-state {
    color: var(--error-color);
}

/* 通知 */
.notification {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: var(--dark-card);
    color: var(--light-text);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--dark-border);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-tooltip);
    transform: translateX(100%);
    transition: var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-color: var(--success-color);
    background: rgba(72, 187, 120, 0.1);
}

.notification.error {
    border-color: var(--error-color);
    background: rgba(245, 101, 101, 0.1);
}

/* 图标字体 */
.icon-shield::before { content: '🛡️'; }
.icon-code::before { content: '💻'; }
.icon-users::before { content: '👥'; }
.icon-trophy::before { content: '🏆'; }
.icon-arrow-right::before { content: '→'; }
.icon-arrow-up::before { content: '↑'; }
.icon-mail::before { content: '✉️'; }
.icon-message-circle::before { content: '💬'; }
.icon-video::before { content: '📹'; }
.icon-github::before { content: '🔗'; }
.icon-external-link::before { content: '🔗'; }
.icon-send::before { content: '📤'; }

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) rotate(-45deg);
    }
    40% {
        transform: translateY(-10px) rotate(-45deg);
    }
    60% {
        transform: translateY(-5px) rotate(-45deg);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--dark-surface);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-xl) 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .container {
        padding: 0 var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .back-to-top {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 45px;
        height: 45px;
    }
}

